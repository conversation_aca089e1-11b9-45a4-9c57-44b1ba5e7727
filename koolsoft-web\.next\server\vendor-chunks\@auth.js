"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/prisma-adapter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@auth/prisma-adapter/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrismaAdapter: () => (/* binding */ PrismaAdapter)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  Official <a href=\"https://www.prisma.io/docs\">Prisma</a> adapter for Auth.js / NextAuth.js.\n *  <a href=\"https://www.prisma.io/\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/prisma.svg\" width=\"38\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @prisma/client @auth/prisma-adapter\n * npm install prisma --save-dev\n * ```\n *\n * @module @auth/prisma-adapter\n */\n\nfunction PrismaAdapter(prisma) {\n  const p = prisma;\n  return {\n    // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB\n    createUser: ({\n      id,\n      ...data\n    }) => p.user.create(stripUndefined(data)),\n    getUser: id => p.user.findUnique({\n      where: {\n        id\n      }\n    }),\n    getUserByEmail: email => p.user.findUnique({\n      where: {\n        email\n      }\n    }),\n    async getUserByAccount(provider_providerAccountId) {\n      const account = await p.account.findUnique({\n        where: {\n          provider_providerAccountId\n        },\n        include: {\n          user: true\n        }\n      });\n      return account?.user ?? null;\n    },\n    updateUser: ({\n      id,\n      ...data\n    }) => p.user.update({\n      where: {\n        id\n      },\n      ...stripUndefined(data)\n    }),\n    deleteUser: id => p.user.delete({\n      where: {\n        id\n      }\n    }),\n    linkAccount: data => p.account.create({\n      data\n    }),\n    unlinkAccount: provider_providerAccountId => p.account.delete({\n      where: {\n        provider_providerAccountId\n      }\n    }),\n    async getSessionAndUser(sessionToken) {\n      const userAndSession = await p.session.findUnique({\n        where: {\n          sessionToken\n        },\n        include: {\n          user: true\n        }\n      });\n      if (!userAndSession) return null;\n      const {\n        user,\n        ...session\n      } = userAndSession;\n      return {\n        user,\n        session\n      };\n    },\n    createSession: data => p.session.create(stripUndefined(data)),\n    updateSession: data => p.session.update({\n      where: {\n        sessionToken: data.sessionToken\n      },\n      ...stripUndefined(data)\n    }),\n    deleteSession: sessionToken => p.session.delete({\n      where: {\n        sessionToken\n      }\n    }),\n    async createVerificationToken(data) {\n      const verificationToken = await p.verificationToken.create(stripUndefined(data));\n      if (\"id\" in verificationToken && verificationToken.id) delete verificationToken.id;\n      return verificationToken;\n    },\n    async useVerificationToken(identifier_token) {\n      try {\n        const verificationToken = await p.verificationToken.delete({\n          where: {\n            identifier_token\n          }\n        });\n        if (\"id\" in verificationToken && verificationToken.id) delete verificationToken.id;\n        return verificationToken;\n      } catch (error) {\n        // If token already used/deleted, just return null\n        // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_0__.Prisma.PrismaClientKnownRequestError && error.code === \"P2025\") return null;\n        throw error;\n      }\n    },\n    async getAccount(providerAccountId, provider) {\n      return p.account.findFirst({\n        where: {\n          providerAccountId,\n          provider\n        }\n      });\n    },\n    async createAuthenticator(data) {\n      return p.authenticator.create(stripUndefined(data));\n    },\n    async getAuthenticator(credentialID) {\n      return p.authenticator.findUnique({\n        where: {\n          credentialID\n        }\n      });\n    },\n    async listAuthenticatorsByUserId(userId) {\n      return p.authenticator.findMany({\n        where: {\n          userId\n        }\n      });\n    },\n    async updateAuthenticatorCounter(credentialID, counter) {\n      return p.authenticator.update({\n        where: {\n          credentialID\n        },\n        data: {\n          counter\n        }\n      });\n    }\n  };\n}\n/** @see https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/null-and-undefined */\nfunction stripUndefined(obj) {\n  const data = {};\n  for (const key in obj) if (obj[key] !== undefined) data[key] = obj[key];\n  return {\n    data\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/prisma-adapter/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@auth/prisma-adapter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@auth/prisma-adapter/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrismaAdapter: () => (/* binding */ PrismaAdapter)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  Official <a href=\"https://www.prisma.io/docs\">Prisma</a> adapter for Auth.js / NextAuth.js.\n *  <a href=\"https://www.prisma.io/\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/prisma.svg\" width=\"38\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @prisma/client @auth/prisma-adapter\n * npm install prisma --save-dev\n * ```\n *\n * @module @auth/prisma-adapter\n */\n\nfunction PrismaAdapter(prisma) {\n  const p = prisma;\n  return {\n    // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB\n    createUser: ({\n      id,\n      ...data\n    }) => p.user.create(stripUndefined(data)),\n    getUser: id => p.user.findUnique({\n      where: {\n        id\n      }\n    }),\n    getUserByEmail: email => p.user.findUnique({\n      where: {\n        email\n      }\n    }),\n    async getUserByAccount(provider_providerAccountId) {\n      const account = await p.account.findUnique({\n        where: {\n          provider_providerAccountId\n        },\n        include: {\n          user: true\n        }\n      });\n      return account?.user ?? null;\n    },\n    updateUser: ({\n      id,\n      ...data\n    }) => p.user.update({\n      where: {\n        id\n      },\n      ...stripUndefined(data)\n    }),\n    deleteUser: id => p.user.delete({\n      where: {\n        id\n      }\n    }),\n    linkAccount: data => p.account.create({\n      data\n    }),\n    unlinkAccount: provider_providerAccountId => p.account.delete({\n      where: {\n        provider_providerAccountId\n      }\n    }),\n    async getSessionAndUser(sessionToken) {\n      const userAndSession = await p.session.findUnique({\n        where: {\n          sessionToken\n        },\n        include: {\n          user: true\n        }\n      });\n      if (!userAndSession) return null;\n      const {\n        user,\n        ...session\n      } = userAndSession;\n      return {\n        user,\n        session\n      };\n    },\n    createSession: data => p.session.create(stripUndefined(data)),\n    updateSession: data => p.session.update({\n      where: {\n        sessionToken: data.sessionToken\n      },\n      ...stripUndefined(data)\n    }),\n    deleteSession: sessionToken => p.session.delete({\n      where: {\n        sessionToken\n      }\n    }),\n    async createVerificationToken(data) {\n      const verificationToken = await p.verificationToken.create(stripUndefined(data));\n      if (\"id\" in verificationToken && verificationToken.id) delete verificationToken.id;\n      return verificationToken;\n    },\n    async useVerificationToken(identifier_token) {\n      try {\n        const verificationToken = await p.verificationToken.delete({\n          where: {\n            identifier_token\n          }\n        });\n        if (\"id\" in verificationToken && verificationToken.id) delete verificationToken.id;\n        return verificationToken;\n      } catch (error) {\n        // If token already used/deleted, just return null\n        // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_0__.Prisma.PrismaClientKnownRequestError && error.code === \"P2025\") return null;\n        throw error;\n      }\n    },\n    async getAccount(providerAccountId, provider) {\n      return p.account.findFirst({\n        where: {\n          providerAccountId,\n          provider\n        }\n      });\n    },\n    async createAuthenticator(data) {\n      return p.authenticator.create(stripUndefined(data));\n    },\n    async getAuthenticator(credentialID) {\n      return p.authenticator.findUnique({\n        where: {\n          credentialID\n        }\n      });\n    },\n    async listAuthenticatorsByUserId(userId) {\n      return p.authenticator.findMany({\n        where: {\n          userId\n        }\n      });\n    },\n    async updateAuthenticatorCounter(credentialID, counter) {\n      return p.authenticator.update({\n        where: {\n          credentialID\n        },\n        data: {\n          counter\n        }\n      });\n    }\n  };\n}\n/** @see https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/null-and-undefined */\nfunction stripUndefined(obj) {\n  const data = {};\n  for (const key in obj) if (obj[key] !== undefined) data[key] = obj[key];\n  return {\n    data\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@auth/prisma-adapter/index.js\n");

/***/ })

};
;