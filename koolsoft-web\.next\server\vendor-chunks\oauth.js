/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUFBLHVHQUE0QztBQUM1Q0EsK0dBQW9EO0FBQ3BEQSwyR0FBK0MiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvYXV0aFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy5PQXV0aCA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aDtcbmV4cG9ydHMuT0F1dGhFY2hvID0gcmVxdWlyZShcIi4vbGliL29hdXRoXCIpLk9BdXRoRWNobztcbmV4cG9ydHMuT0F1dGgyID0gcmVxdWlyZShcIi4vbGliL29hdXRoMlwiKS5PQXV0aDI7Il0sIm5hbWVzIjpbImV4cG9ydHMiLCJPQXV0aCIsInJlcXVpcmUiLCJPQXV0aEVjaG8iLCJPQXV0aDIiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

"use strict";
eval("\n\n// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost = function (hostName) {\n  return hostName && hostName.match(\".*google(apis)?.com$\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiOztBQUFBO0FBQ0FBLGlDQUFpQyxHQUFFLFVBQVVHLFFBQVEsRUFBRztFQUN0RCxPQUFPQSxRQUFRLElBQUlBLFFBQVEsQ0FBQ0MsS0FBSyxDQUFDLHNCQUFzQixDQUFDO0FBQzNELENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvYXV0aFxcbGliXFxfdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmV0dXJucyB0cnVlIGlmIHRoaXMgaXMgYSBob3N0IHRoYXQgY2xvc2VzICpiZWZvcmUqIGl0IGVuZHM/IT8hXG5tb2R1bGUuZXhwb3J0cy5pc0FuRWFybHlDbG9zZUhvc3Q9IGZ1bmN0aW9uKCBob3N0TmFtZSApIHtcbiAgcmV0dXJuIGhvc3ROYW1lICYmIGhvc3ROYW1lLm1hdGNoKFwiLipnb29nbGUoYXBpcyk/LmNvbSRcIilcbn0iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImlzQW5FYXJseUNsb3NlSG9zdCIsImhvc3ROYW1lIiwibWF0Y2giXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\"),\n  sha1 = __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"),\n  http = __webpack_require__(/*! http */ \"http\"),\n  https = __webpack_require__(/*! https */ \"https\"),\n  URL = __webpack_require__(/*! url */ \"url\"),\n  querystring = __webpack_require__(/*! querystring */ \"querystring\"),\n  OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth = function (requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = false;\n  this._requestUrl = requestUrl;\n  this._accessUrl = accessUrl;\n  this._consumerKey = consumerKey;\n  this._consumerSecret = this._encodeData(consumerSecret);\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version = version;\n  if (authorize_callback === undefined) {\n    this._authorize_callback = \"oob\";\n  } else {\n    this._authorize_callback = authorize_callback;\n  }\n  if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n  this._signatureMethod = signatureMethod;\n  this._nonceSize = nonceSize || 32;\n  this._headers = customHeaders || {\n    \"Accept\": \"*/*\",\n    \"Connection\": \"close\",\n    \"User-Agent\": \"Node authentication\"\n  };\n  this._clientOptions = this._defaultClientOptions = {\n    \"requestTokenHttpMethod\": \"POST\",\n    \"accessTokenHttpMethod\": \"POST\",\n    \"followRedirects\": true\n  };\n  this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho = function (realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = true;\n  this._realm = realm;\n  this._verifyCredentials = verify_credentials;\n  this._consumerKey = consumerKey;\n  this._consumerSecret = this._encodeData(consumerSecret);\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version = version;\n  if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n  this._signatureMethod = signatureMethod;\n  this._nonceSize = nonceSize || 32;\n  this._headers = customHeaders || {\n    \"Accept\": \"*/*\",\n    \"Connection\": \"close\",\n    \"User-Agent\": \"Node authentication\"\n  };\n  this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\nexports.OAuth.prototype._getTimestamp = function () {\n  return Math.floor(new Date().getTime() / 1000);\n};\nexports.OAuth.prototype._encodeData = function (toEncode) {\n  if (toEncode == null || toEncode == \"\") return \"\";else {\n    var result = encodeURIComponent(toEncode);\n    // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n    return result.replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n  }\n};\nexports.OAuth.prototype._decodeData = function (toDecode) {\n  if (toDecode != null) {\n    toDecode = toDecode.replace(/\\+/g, \" \");\n  }\n  return decodeURIComponent(toDecode);\n};\nexports.OAuth.prototype._getSignature = function (method, url, parameters, tokenSecret) {\n  var signatureBase = this._createSignatureBase(method, url, parameters);\n  return this._createSignature(signatureBase, tokenSecret);\n};\nexports.OAuth.prototype._normalizeUrl = function (url) {\n  var parsedUrl = URL.parse(url, true);\n  var port = \"\";\n  if (parsedUrl.port) {\n    if (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" || parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") {\n      port = \":\" + parsedUrl.port;\n    }\n  }\n  if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n  return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n};\n\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter = function (parameter) {\n  var m = parameter.match('^oauth_');\n  if (m && m[0] === \"oauth_\") {\n    return true;\n  } else {\n    return false;\n  }\n};\n\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders = function (orderedParameters) {\n  var authHeader = \"OAuth \";\n  if (this._isEcho) {\n    authHeader += 'realm=\"' + this._realm + '\",';\n  }\n  for (var i = 0; i < orderedParameters.length; i++) {\n    // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n    // should appear within the authorization header.\n    if (this._isParameterNameAnOAuthParameter(orderedParameters[i][0])) {\n      authHeader += \"\" + this._encodeData(orderedParameters[i][0]) + \"=\\\"\" + this._encodeData(orderedParameters[i][1]) + \"\\\"\" + this._oauthParameterSeperator;\n    }\n  }\n  authHeader = authHeader.substring(0, authHeader.length - this._oauthParameterSeperator.length);\n  return authHeader;\n};\n\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash = function (argumentsHash) {\n  var argument_pairs = [];\n  for (var key in argumentsHash) {\n    if (argumentsHash.hasOwnProperty(key)) {\n      var value = argumentsHash[key];\n      if (Array.isArray(value)) {\n        for (var i = 0; i < value.length; i++) {\n          argument_pairs[argument_pairs.length] = [key, value[i]];\n        }\n      } else {\n        argument_pairs[argument_pairs.length] = [key, value];\n      }\n    }\n  }\n  return argument_pairs;\n};\n\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams = function (argument_pairs) {\n  // Sort by name, then value.\n  argument_pairs.sort(function (a, b) {\n    if (a[0] == b[0]) {\n      return a[1] < b[1] ? -1 : 1;\n    } else return a[0] < b[0] ? -1 : 1;\n  });\n  return argument_pairs;\n};\nexports.OAuth.prototype._normaliseRequestParams = function (args) {\n  var argument_pairs = this._makeArrayOfArgumentsHash(args);\n  // First encode them #3.4.1.3.2 .1\n  for (var i = 0; i < argument_pairs.length; i++) {\n    argument_pairs[i][0] = this._encodeData(argument_pairs[i][0]);\n    argument_pairs[i][1] = this._encodeData(argument_pairs[i][1]);\n  }\n\n  // Then sort them #3.4.1.3.2 .2\n  argument_pairs = this._sortRequestParams(argument_pairs);\n\n  // Then concatenate together #3.4.1.3.2 .3 & .4\n  var args = \"\";\n  for (var i = 0; i < argument_pairs.length; i++) {\n    args += argument_pairs[i][0];\n    args += \"=\";\n    args += argument_pairs[i][1];\n    if (i < argument_pairs.length - 1) args += \"&\";\n  }\n  return args;\n};\nexports.OAuth.prototype._createSignatureBase = function (method, url, parameters) {\n  url = this._encodeData(this._normalizeUrl(url));\n  parameters = this._encodeData(parameters);\n  return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n};\nexports.OAuth.prototype._createSignature = function (signatureBase, tokenSecret) {\n  if (tokenSecret === undefined) var tokenSecret = \"\";else tokenSecret = this._encodeData(tokenSecret);\n  // consumerSecret is already encoded\n  var key = this._consumerSecret + \"&\" + tokenSecret;\n  var hash = \"\";\n  if (this._signatureMethod == \"PLAINTEXT\") {\n    hash = key;\n  } else if (this._signatureMethod == \"RSA-SHA1\") {\n    key = this._privateKey || \"\";\n    hash = crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, 'base64');\n  } else {\n    if (crypto.Hmac) {\n      hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n    } else {\n      hash = sha1.HMACSHA1(key, signatureBase);\n    }\n  }\n  return hash;\n};\nexports.OAuth.prototype.NONCE_CHARS = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nexports.OAuth.prototype._getNonce = function (nonceSize) {\n  var result = [];\n  var chars = this.NONCE_CHARS;\n  var char_pos;\n  var nonce_chars_length = chars.length;\n  for (var i = 0; i < nonceSize; i++) {\n    char_pos = Math.floor(Math.random() * nonce_chars_length);\n    result[i] = chars[char_pos];\n  }\n  return result.join('');\n};\nexports.OAuth.prototype._createClient = function (port, hostname, method, path, headers, sslEnabled) {\n  var options = {\n    host: hostname,\n    port: port,\n    path: path,\n    method: method,\n    headers: headers\n  };\n  var httpModel;\n  if (sslEnabled) {\n    httpModel = https;\n  } else {\n    httpModel = http;\n  }\n  return httpModel.request(options);\n};\nexports.OAuth.prototype._prepareParameters = function (oauth_token, oauth_token_secret, method, url, extra_params) {\n  var oauthParameters = {\n    \"oauth_timestamp\": this._getTimestamp(),\n    \"oauth_nonce\": this._getNonce(this._nonceSize),\n    \"oauth_version\": this._version,\n    \"oauth_signature_method\": this._signatureMethod,\n    \"oauth_consumer_key\": this._consumerKey\n  };\n  if (oauth_token) {\n    oauthParameters[\"oauth_token\"] = oauth_token;\n  }\n  var sig;\n  if (this._isEcho) {\n    sig = this._getSignature(\"GET\", this._verifyCredentials, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  } else {\n    if (extra_params) {\n      for (var key in extra_params) {\n        if (extra_params.hasOwnProperty(key)) oauthParameters[key] = extra_params[key];\n      }\n    }\n    var parsedUrl = URL.parse(url, false);\n    if (parsedUrl.query) {\n      var key2;\n      var extraParameters = querystring.parse(parsedUrl.query);\n      for (var key in extraParameters) {\n        var value = extraParameters[key];\n        if (typeof value == \"object\") {\n          // TODO: This probably should be recursive\n          for (key2 in value) {\n            oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n          }\n        } else {\n          oauthParameters[key] = value;\n        }\n      }\n    }\n    sig = this._getSignature(method, url, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n  var orderedParameters = this._sortRequestParams(this._makeArrayOfArgumentsHash(oauthParameters));\n  orderedParameters[orderedParameters.length] = [\"oauth_signature\", sig];\n  return orderedParameters;\n};\nexports.OAuth.prototype._performSecureRequest = function (oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback) {\n  var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n  if (!post_content_type) {\n    post_content_type = \"application/x-www-form-urlencoded\";\n  }\n  var parsedUrl = URL.parse(url, false);\n  if (parsedUrl.protocol == \"http:\" && !parsedUrl.port) parsedUrl.port = 80;\n  if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) parsedUrl.port = 443;\n  var headers = {};\n  var authorization = this._buildAuthorizationHeaders(orderedParameters);\n  if (this._isEcho) {\n    headers[\"X-Verify-Credentials-Authorization\"] = authorization;\n  } else {\n    headers[\"Authorization\"] = authorization;\n  }\n  headers[\"Host\"] = parsedUrl.host;\n  for (var key in this._headers) {\n    if (this._headers.hasOwnProperty(key)) {\n      headers[key] = this._headers[key];\n    }\n  }\n\n  // Filter out any passed extra_params that are really to do with OAuth\n  for (var key in extra_params) {\n    if (this._isParameterNameAnOAuthParameter(key)) {\n      delete extra_params[key];\n    }\n  }\n  if ((method == \"POST\" || method == \"PUT\") && post_body == null && extra_params != null) {\n    // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n    post_body = querystring.stringify(extra_params).replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n  }\n  if (post_body) {\n    if (Buffer.isBuffer(post_body)) {\n      headers[\"Content-length\"] = post_body.length;\n    } else {\n      headers[\"Content-length\"] = Buffer.byteLength(post_body);\n    }\n  } else {\n    headers[\"Content-length\"] = 0;\n  }\n  headers[\"Content-Type\"] = post_content_type;\n  var path;\n  if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n  if (parsedUrl.query) path = parsedUrl.pathname + \"?\" + parsedUrl.query;else path = parsedUrl.pathname;\n  var request;\n  if (parsedUrl.protocol == \"https:\") {\n    request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n  } else {\n    request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n  }\n  var clientOptions = this._clientOptions;\n  if (callback) {\n    var data = \"\";\n    var self = this;\n\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(parsedUrl.hostname);\n    var callbackCalled = false;\n    var passBackControl = function (response) {\n      if (!callbackCalled) {\n        callbackCalled = true;\n        if (response.statusCode >= 200 && response.statusCode <= 299) {\n          callback(null, data, response);\n        } else {\n          // Follow 301 or 302 redirects with Location HTTP header\n          if ((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n            self._performSecureRequest(oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type, callback);\n          } else {\n            callback({\n              statusCode: response.statusCode,\n              data: data\n            }, data, response);\n          }\n        }\n      }\n    };\n    request.on('response', function (response) {\n      response.setEncoding('utf8');\n      response.on('data', function (chunk) {\n        data += chunk;\n      });\n      response.on('end', function () {\n        passBackControl(response);\n      });\n      response.on('close', function () {\n        if (allowEarlyClose) {\n          passBackControl(response);\n        }\n      });\n    });\n    request.on(\"error\", function (err) {\n      if (!callbackCalled) {\n        callbackCalled = true;\n        callback(err);\n      }\n    });\n    if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n      request.write(post_body);\n    }\n    request.end();\n  } else {\n    if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n      request.write(post_body);\n    }\n    return request;\n  }\n  return;\n};\nexports.OAuth.prototype.setClientOptions = function (options) {\n  var key,\n    mergedOptions = {},\n    hasOwnProperty = Object.prototype.hasOwnProperty;\n  for (key in this._defaultClientOptions) {\n    if (!hasOwnProperty.call(options, key)) {\n      mergedOptions[key] = this._defaultClientOptions[key];\n    } else {\n      mergedOptions[key] = options[key];\n    }\n  }\n  this._clientOptions = mergedOptions;\n};\nexports.OAuth.prototype.getOAuthAccessToken = function (oauth_token, oauth_token_secret, oauth_verifier, callback) {\n  var extraParams = {};\n  if (typeof oauth_verifier == \"function\") {\n    callback = oauth_verifier;\n  } else {\n    extraParams.oauth_verifier = oauth_verifier;\n  }\n  this._performSecureRequest(oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function (error, data, response) {\n    if (error) callback(error);else {\n      var results = querystring.parse(data);\n      var oauth_access_token = results[\"oauth_token\"];\n      delete results[\"oauth_token\"];\n      var oauth_access_token_secret = results[\"oauth_token_secret\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_access_token, oauth_access_token_secret, results);\n    }\n  });\n};\n\n// Deprecated\nexports.OAuth.prototype.getProtectedResource = function (url, method, oauth_token, oauth_token_secret, callback) {\n  this._performSecureRequest(oauth_token, oauth_token_secret, method, url, null, \"\", null, callback);\n};\nexports.OAuth.prototype[\"delete\"] = function (url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest(oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype.get = function (url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest(oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype._putOrPost = function (method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  var extra_params = null;\n  if (typeof post_content_type == \"function\") {\n    callback = post_content_type;\n    post_content_type = null;\n  }\n  if (typeof post_body != \"string\" && !Buffer.isBuffer(post_body)) {\n    post_content_type = \"application/x-www-form-urlencoded\";\n    extra_params = post_body;\n    post_body = null;\n  }\n  return this._performSecureRequest(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.put = function (url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.post = function (url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\n\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/\nexports.OAuth.prototype.getOAuthRequestToken = function (extraParams, callback) {\n  if (typeof extraParams == \"function\") {\n    callback = extraParams;\n    extraParams = {};\n  }\n  // Callbacks are 1.0A related\n  if (this._authorize_callback) {\n    extraParams[\"oauth_callback\"] = this._authorize_callback;\n  }\n  this._performSecureRequest(null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function (error, data, response) {\n    if (error) callback(error);else {\n      var results = querystring.parse(data);\n      var oauth_token = results[\"oauth_token\"];\n      var oauth_token_secret = results[\"oauth_token_secret\"];\n      delete results[\"oauth_token\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_token, oauth_token_secret, results);\n    }\n  });\n};\nexports.OAuth.prototype.signUrl = function (url, oauth_token, oauth_token_secret, method) {\n  if (method === undefined) {\n    var method = \"GET\";\n  }\n  var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  var parsedUrl = URL.parse(url, false);\n  var query = \"\";\n  for (var i = 0; i < orderedParameters.length; i++) {\n    query += orderedParameters[i][0] + \"=\" + this._encodeData(orderedParameters[i][1]) + \"&\";\n  }\n  query = query.substring(0, query.length - 1);\n  return parsedUrl.protocol + \"//\" + parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\nexports.OAuth.prototype.authHeader = function (url, oauth_token, oauth_token_secret, method) {\n  if (method === undefined) {\n    var method = \"GET\";\n  }\n  var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  return this._buildAuthorizationHeaders(orderedParameters);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring = __webpack_require__(/*! querystring */ \"querystring\"),\n  crypto = __webpack_require__(/*! crypto */ \"crypto\"),\n  https = __webpack_require__(/*! https */ \"https\"),\n  http = __webpack_require__(/*! http */ \"http\"),\n  URL = __webpack_require__(/*! url */ \"url\"),\n  OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth2 = function (clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n  this._clientId = clientId;\n  this._clientSecret = clientSecret;\n  this._baseSite = baseSite;\n  this._authorizeUrl = authorizePath || \"/oauth/authorize\";\n  this._accessTokenUrl = accessTokenPath || \"/oauth/access_token\";\n  this._accessTokenName = \"access_token\";\n  this._authMethod = \"Bearer\";\n  this._customHeaders = customHeaders || {};\n  this._useAuthorizationHeaderForGET = false;\n\n  //our agent\n  this._agent = undefined;\n};\n\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function (agent) {\n  this._agent = agent;\n};\n\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName = function (name) {\n  this._accessTokenName = name;\n};\n\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function (authMethod) {\n  this._authMethod = authMethod;\n};\n\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function (useIt) {\n  this._useAuthorizationHeaderForGET = useIt;\n};\nexports.OAuth2.prototype._getAccessTokenUrl = function () {\n  return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */\n};\n\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader = function (token) {\n  return this._authMethod + ' ' + token;\n};\nexports.OAuth2.prototype._chooseHttpLibrary = function (parsedUrl) {\n  var http_library = https;\n  // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n  if (parsedUrl.protocol != \"https:\") {\n    http_library = http;\n  }\n  return http_library;\n};\nexports.OAuth2.prototype._request = function (method, url, headers, post_body, access_token, callback) {\n  var parsedUrl = URL.parse(url, true);\n  if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) {\n    parsedUrl.port = 443;\n  }\n  var http_library = this._chooseHttpLibrary(parsedUrl);\n  var realHeaders = {};\n  for (var key in this._customHeaders) {\n    realHeaders[key] = this._customHeaders[key];\n  }\n  if (headers) {\n    for (var key in headers) {\n      realHeaders[key] = headers[key];\n    }\n  }\n  realHeaders['Host'] = parsedUrl.host;\n  if (!realHeaders['User-Agent']) {\n    realHeaders['User-Agent'] = 'Node-oauth';\n  }\n  if (post_body) {\n    if (Buffer.isBuffer(post_body)) {\n      realHeaders[\"Content-Length\"] = post_body.length;\n    } else {\n      realHeaders[\"Content-Length\"] = Buffer.byteLength(post_body);\n    }\n  } else {\n    realHeaders[\"Content-length\"] = 0;\n  }\n  if (access_token && !('Authorization' in realHeaders)) {\n    if (!parsedUrl.query) parsedUrl.query = {};\n    parsedUrl.query[this._accessTokenName] = access_token;\n  }\n  var queryStr = querystring.stringify(parsedUrl.query);\n  if (queryStr) queryStr = \"?\" + queryStr;\n  var options = {\n    host: parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname + queryStr,\n    method: method,\n    headers: realHeaders\n  };\n  this._executeRequest(http_library, options, post_body, callback);\n};\nexports.OAuth2.prototype._executeRequest = function (http_library, options, post_body, callback) {\n  // Some hosts *cough* google appear to close the connection early / send no content-length header\n  // allow this behaviour.\n  var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(options.host);\n  var callbackCalled = false;\n  function passBackControl(response, result) {\n    if (!callbackCalled) {\n      callbackCalled = true;\n      if (!(response.statusCode >= 200 && response.statusCode <= 299) && response.statusCode != 301 && response.statusCode != 302) {\n        callback({\n          statusCode: response.statusCode,\n          data: result\n        });\n      } else {\n        callback(null, result, response);\n      }\n    }\n  }\n  var result = \"\";\n\n  //set the agent on the request options\n  if (this._agent) {\n    options.agent = this._agent;\n  }\n  var request = http_library.request(options);\n  request.on('response', function (response) {\n    response.on(\"data\", function (chunk) {\n      result += chunk;\n    });\n    response.on(\"close\", function (err) {\n      if (allowEarlyClose) {\n        passBackControl(response, result);\n      }\n    });\n    response.addListener(\"end\", function () {\n      passBackControl(response, result);\n    });\n  });\n  request.on('error', function (e) {\n    callbackCalled = true;\n    callback(e);\n  });\n  if ((options.method == 'POST' || options.method == 'PUT') && post_body) {\n    request.write(post_body);\n  }\n  request.end();\n};\nexports.OAuth2.prototype.getAuthorizeUrl = function (params) {\n  var params = params || {};\n  params['client_id'] = this._clientId;\n  return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n};\nexports.OAuth2.prototype.getOAuthAccessToken = function (code, params, callback) {\n  var params = params || {};\n  params['client_id'] = this._clientId;\n  params['client_secret'] = this._clientSecret;\n  var codeParam = params.grant_type === 'refresh_token' ? 'refresh_token' : 'code';\n  params[codeParam] = code;\n  var post_data = querystring.stringify(params);\n  var post_headers = {\n    'Content-Type': 'application/x-www-form-urlencoded'\n  };\n  this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function (error, data, response) {\n    if (error) callback(error);else {\n      var results;\n      try {\n        // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n        // responses should be in JSON\n        results = JSON.parse(data);\n      } catch (e) {\n        // .... However both Facebook + Github currently use rev05 of the spec\n        // and neither seem to specify a content-type correctly in their response headers :(\n        // clients of these services will suffer a *minor* performance cost of the exception\n        // being thrown\n        results = querystring.parse(data);\n      }\n      var access_token = results[\"access_token\"];\n      var refresh_token = results[\"refresh_token\"];\n      delete results[\"refresh_token\"];\n      callback(null, access_token, refresh_token, results); // callback results =-=\n    }\n  });\n};\n\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource = function (url, access_token, callback) {\n  this._request(\"GET\", url, {}, \"\", access_token, callback);\n};\nexports.OAuth2.prototype.get = function (url, access_token, callback) {\n  if (this._useAuthorizationHeaderForGET) {\n    var headers = {\n      'Authorization': this.buildAuthHeader(access_token)\n    };\n    access_token = null;\n  } else {\n    headers = {};\n  }\n  this._request(\"GET\", url, headers, \"\", access_token, callback);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */\n\n/*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */\nvar hexcase = 1; /* hex output format. 0 - lowercase; 1 - uppercase        */\nvar b64pad = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n\n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */\nfunction hex_sha1(s) {\n  return rstr2hex(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction b64_sha1(s) {\n  return rstr2b64(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction any_sha1(s, e) {\n  return rstr2any(rstr_sha1(str2rstr_utf8(s)), e);\n}\nfunction hex_hmac_sha1(k, d) {\n  return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction b64_hmac_sha1(k, d) {\n  return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction any_hmac_sha1(k, d, e) {\n  return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e);\n}\n\n/*\n * Perform a simple self-test to see if the VM is working\n */\nfunction sha1_vm_test() {\n  return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n\n/*\n * Calculate the SHA1 of a raw string\n */\nfunction rstr_sha1(s) {\n  return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */\nfunction rstr_hmac_sha1(key, data) {\n  var bkey = rstr2binb(key);\n  if (bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n  var ipad = Array(16),\n    opad = Array(16);\n  for (var i = 0; i < 16; i++) {\n    ipad[i] = bkey[i] ^ 0x36363636;\n    opad[i] = bkey[i] ^ 0x5C5C5C5C;\n  }\n  var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n  return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n\n/*\n * Convert a raw string to a hex string\n */\nfunction rstr2hex(input) {\n  try {\n    hexcase;\n  } catch (e) {\n    hexcase = 0;\n  }\n  var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n  var output = \"\";\n  var x;\n  for (var i = 0; i < input.length; i++) {\n    x = input.charCodeAt(i);\n    output += hex_tab.charAt(x >>> 4 & 0x0F) + hex_tab.charAt(x & 0x0F);\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to a base-64 string\n */\nfunction rstr2b64(input) {\n  try {\n    b64pad;\n  } catch (e) {\n    b64pad = '';\n  }\n  var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n  var output = \"\";\n  var len = input.length;\n  for (var i = 0; i < len; i += 3) {\n    var triplet = input.charCodeAt(i) << 16 | (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) | (i + 2 < len ? input.charCodeAt(i + 2) : 0);\n    for (var j = 0; j < 4; j++) {\n      if (i * 8 + j * 6 > input.length * 8) output += b64pad;else output += tab.charAt(triplet >>> 6 * (3 - j) & 0x3F);\n    }\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to an arbitrary string encoding\n */\nfunction rstr2any(input, encoding) {\n  var divisor = encoding.length;\n  var remainders = Array();\n  var i, q, x, quotient;\n\n  /* Convert to an array of 16-bit big-endian values, forming the dividend */\n  var dividend = Array(Math.ceil(input.length / 2));\n  for (i = 0; i < dividend.length; i++) {\n    dividend[i] = input.charCodeAt(i * 2) << 8 | input.charCodeAt(i * 2 + 1);\n  }\n\n  /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */\n  while (dividend.length > 0) {\n    quotient = Array();\n    x = 0;\n    for (i = 0; i < dividend.length; i++) {\n      x = (x << 16) + dividend[i];\n      q = Math.floor(x / divisor);\n      x -= q * divisor;\n      if (quotient.length > 0 || q > 0) quotient[quotient.length] = q;\n    }\n    remainders[remainders.length] = x;\n    dividend = quotient;\n  }\n\n  /* Convert the remainders to the output string */\n  var output = \"\";\n  for (i = remainders.length - 1; i >= 0; i--) output += encoding.charAt(remainders[i]);\n\n  /* Append leading zero equivalents */\n  var full_length = Math.ceil(input.length * 8 / (Math.log(encoding.length) / Math.log(2)));\n  for (i = output.length; i < full_length; i++) output = encoding[0] + output;\n  return output;\n}\n\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */\nfunction str2rstr_utf8(input) {\n  var output = \"\";\n  var i = -1;\n  var x, y;\n  while (++i < input.length) {\n    /* Decode utf-16 surrogate pairs */\n    x = input.charCodeAt(i);\n    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n    if (0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF) {\n      x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n      i++;\n    }\n\n    /* Encode output as utf-8 */\n    if (x <= 0x7F) output += String.fromCharCode(x);else if (x <= 0x7FF) output += String.fromCharCode(0xC0 | x >>> 6 & 0x1F, 0x80 | x & 0x3F);else if (x <= 0xFFFF) output += String.fromCharCode(0xE0 | x >>> 12 & 0x0F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);else if (x <= 0x1FFFFF) output += String.fromCharCode(0xF0 | x >>> 18 & 0x07, 0x80 | x >>> 12 & 0x3F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n  }\n  return output;\n}\n\n/*\n * Encode a string as utf-16\n */\nfunction str2rstr_utf16le(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length; i++) output += String.fromCharCode(input.charCodeAt(i) & 0xFF, input.charCodeAt(i) >>> 8 & 0xFF);\n  return output;\n}\nfunction str2rstr_utf16be(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length; i++) output += String.fromCharCode(input.charCodeAt(i) >>> 8 & 0xFF, input.charCodeAt(i) & 0xFF);\n  return output;\n}\n\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input) {\n  var output = Array(input.length >> 2);\n  for (var i = 0; i < output.length; i++) output[i] = 0;\n  for (var i = 0; i < input.length * 8; i += 8) output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << 24 - i % 32;\n  return output;\n}\n\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length * 32; i += 8) output += String.fromCharCode(input[i >> 5] >>> 24 - i % 32 & 0xFF);\n  return output;\n}\n\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */\nfunction binb_sha1(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << 24 - len % 32;\n  x[(len + 64 >> 9 << 4) + 15] = len;\n  var w = Array(80);\n  var a = 1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d = 271733878;\n  var e = -1009589776;\n  for (var i = 0; i < x.length; i += 16) {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    var olde = e;\n    for (var j = 0; j < 80; j++) {\n      if (j < 16) w[j] = x[i + j];else w[j] = bit_rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)), safe_add(safe_add(e, w[j]), sha1_kt(j)));\n      e = d;\n      d = c;\n      c = bit_rol(b, 30);\n      b = a;\n      a = t;\n    }\n    a = safe_add(a, olda);\n    b = safe_add(b, oldb);\n    c = safe_add(c, oldc);\n    d = safe_add(d, oldd);\n    e = safe_add(e, olde);\n  }\n  return Array(a, b, c, d, e);\n}\n\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */\nfunction sha1_ft(t, b, c, d) {\n  if (t < 20) return b & c | ~b & d;\n  if (t < 40) return b ^ c ^ d;\n  if (t < 60) return b & c | b & d | c & d;\n  return b ^ c ^ d;\n}\n\n/*\n * Determine the appropriate additive constant for the current iteration\n */\nfunction sha1_kt(t) {\n  return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;\n}\n\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\nfunction safe_add(x, y) {\n  var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xFFFF;\n}\n\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\nfunction bit_rol(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\nexports.HMACSHA1 = function (key, data) {\n  return b64_hmac_sha1(key, data);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(ssr)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(ssr)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(ssr)/./node_modules/oauth/lib/oauth2.js\").OAuth2;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUFBLHVHQUE0QztBQUM1Q0EsK0dBQW9EO0FBQ3BEQSwyR0FBK0MiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvYXV0aFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy5PQXV0aCA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aDtcbmV4cG9ydHMuT0F1dGhFY2hvID0gcmVxdWlyZShcIi4vbGliL29hdXRoXCIpLk9BdXRoRWNobztcbmV4cG9ydHMuT0F1dGgyID0gcmVxdWlyZShcIi4vbGliL29hdXRoMlwiKS5PQXV0aDI7Il0sIm5hbWVzIjpbImV4cG9ydHMiLCJPQXV0aCIsInJlcXVpcmUiLCJPQXV0aEVjaG8iLCJPQXV0aDIiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

"use strict";
eval("\n\n// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost = function (hostName) {\n  return hostName && hostName.match(\".*google(apis)?.com$\");\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiOztBQUFBO0FBQ0FBLGlDQUFpQyxHQUFFLFVBQVVHLFFBQVEsRUFBRztFQUN0RCxPQUFPQSxRQUFRLElBQUlBLFFBQVEsQ0FBQ0MsS0FBSyxDQUFDLHNCQUFzQixDQUFDO0FBQzNELENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvYXV0aFxcbGliXFxfdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmV0dXJucyB0cnVlIGlmIHRoaXMgaXMgYSBob3N0IHRoYXQgY2xvc2VzICpiZWZvcmUqIGl0IGVuZHM/IT8hXG5tb2R1bGUuZXhwb3J0cy5pc0FuRWFybHlDbG9zZUhvc3Q9IGZ1bmN0aW9uKCBob3N0TmFtZSApIHtcbiAgcmV0dXJuIGhvc3ROYW1lICYmIGhvc3ROYW1lLm1hdGNoKFwiLipnb29nbGUoYXBpcyk/LmNvbSRcIilcbn0iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImlzQW5FYXJseUNsb3NlSG9zdCIsImhvc3ROYW1lIiwibWF0Y2giXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\"),\n  sha1 = __webpack_require__(/*! ./sha1 */ \"(ssr)/./node_modules/oauth/lib/sha1.js\"),\n  http = __webpack_require__(/*! http */ \"http\"),\n  https = __webpack_require__(/*! https */ \"https\"),\n  URL = __webpack_require__(/*! url */ \"url\"),\n  querystring = __webpack_require__(/*! querystring */ \"querystring\"),\n  OAuthUtils = __webpack_require__(/*! ./_utils */ \"(ssr)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth = function (requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = false;\n  this._requestUrl = requestUrl;\n  this._accessUrl = accessUrl;\n  this._consumerKey = consumerKey;\n  this._consumerSecret = this._encodeData(consumerSecret);\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version = version;\n  if (authorize_callback === undefined) {\n    this._authorize_callback = \"oob\";\n  } else {\n    this._authorize_callback = authorize_callback;\n  }\n  if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n  this._signatureMethod = signatureMethod;\n  this._nonceSize = nonceSize || 32;\n  this._headers = customHeaders || {\n    \"Accept\": \"*/*\",\n    \"Connection\": \"close\",\n    \"User-Agent\": \"Node authentication\"\n  };\n  this._clientOptions = this._defaultClientOptions = {\n    \"requestTokenHttpMethod\": \"POST\",\n    \"accessTokenHttpMethod\": \"POST\",\n    \"followRedirects\": true\n  };\n  this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho = function (realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = true;\n  this._realm = realm;\n  this._verifyCredentials = verify_credentials;\n  this._consumerKey = consumerKey;\n  this._consumerSecret = this._encodeData(consumerSecret);\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version = version;\n  if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n  this._signatureMethod = signatureMethod;\n  this._nonceSize = nonceSize || 32;\n  this._headers = customHeaders || {\n    \"Accept\": \"*/*\",\n    \"Connection\": \"close\",\n    \"User-Agent\": \"Node authentication\"\n  };\n  this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\nexports.OAuth.prototype._getTimestamp = function () {\n  return Math.floor(new Date().getTime() / 1000);\n};\nexports.OAuth.prototype._encodeData = function (toEncode) {\n  if (toEncode == null || toEncode == \"\") return \"\";else {\n    var result = encodeURIComponent(toEncode);\n    // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n    return result.replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n  }\n};\nexports.OAuth.prototype._decodeData = function (toDecode) {\n  if (toDecode != null) {\n    toDecode = toDecode.replace(/\\+/g, \" \");\n  }\n  return decodeURIComponent(toDecode);\n};\nexports.OAuth.prototype._getSignature = function (method, url, parameters, tokenSecret) {\n  var signatureBase = this._createSignatureBase(method, url, parameters);\n  return this._createSignature(signatureBase, tokenSecret);\n};\nexports.OAuth.prototype._normalizeUrl = function (url) {\n  var parsedUrl = URL.parse(url, true);\n  var port = \"\";\n  if (parsedUrl.port) {\n    if (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" || parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") {\n      port = \":\" + parsedUrl.port;\n    }\n  }\n  if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n  return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n};\n\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter = function (parameter) {\n  var m = parameter.match('^oauth_');\n  if (m && m[0] === \"oauth_\") {\n    return true;\n  } else {\n    return false;\n  }\n};\n\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders = function (orderedParameters) {\n  var authHeader = \"OAuth \";\n  if (this._isEcho) {\n    authHeader += 'realm=\"' + this._realm + '\",';\n  }\n  for (var i = 0; i < orderedParameters.length; i++) {\n    // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n    // should appear within the authorization header.\n    if (this._isParameterNameAnOAuthParameter(orderedParameters[i][0])) {\n      authHeader += \"\" + this._encodeData(orderedParameters[i][0]) + \"=\\\"\" + this._encodeData(orderedParameters[i][1]) + \"\\\"\" + this._oauthParameterSeperator;\n    }\n  }\n  authHeader = authHeader.substring(0, authHeader.length - this._oauthParameterSeperator.length);\n  return authHeader;\n};\n\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash = function (argumentsHash) {\n  var argument_pairs = [];\n  for (var key in argumentsHash) {\n    if (argumentsHash.hasOwnProperty(key)) {\n      var value = argumentsHash[key];\n      if (Array.isArray(value)) {\n        for (var i = 0; i < value.length; i++) {\n          argument_pairs[argument_pairs.length] = [key, value[i]];\n        }\n      } else {\n        argument_pairs[argument_pairs.length] = [key, value];\n      }\n    }\n  }\n  return argument_pairs;\n};\n\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams = function (argument_pairs) {\n  // Sort by name, then value.\n  argument_pairs.sort(function (a, b) {\n    if (a[0] == b[0]) {\n      return a[1] < b[1] ? -1 : 1;\n    } else return a[0] < b[0] ? -1 : 1;\n  });\n  return argument_pairs;\n};\nexports.OAuth.prototype._normaliseRequestParams = function (args) {\n  var argument_pairs = this._makeArrayOfArgumentsHash(args);\n  // First encode them #3.4.1.3.2 .1\n  for (var i = 0; i < argument_pairs.length; i++) {\n    argument_pairs[i][0] = this._encodeData(argument_pairs[i][0]);\n    argument_pairs[i][1] = this._encodeData(argument_pairs[i][1]);\n  }\n\n  // Then sort them #3.4.1.3.2 .2\n  argument_pairs = this._sortRequestParams(argument_pairs);\n\n  // Then concatenate together #3.4.1.3.2 .3 & .4\n  var args = \"\";\n  for (var i = 0; i < argument_pairs.length; i++) {\n    args += argument_pairs[i][0];\n    args += \"=\";\n    args += argument_pairs[i][1];\n    if (i < argument_pairs.length - 1) args += \"&\";\n  }\n  return args;\n};\nexports.OAuth.prototype._createSignatureBase = function (method, url, parameters) {\n  url = this._encodeData(this._normalizeUrl(url));\n  parameters = this._encodeData(parameters);\n  return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n};\nexports.OAuth.prototype._createSignature = function (signatureBase, tokenSecret) {\n  if (tokenSecret === undefined) var tokenSecret = \"\";else tokenSecret = this._encodeData(tokenSecret);\n  // consumerSecret is already encoded\n  var key = this._consumerSecret + \"&\" + tokenSecret;\n  var hash = \"\";\n  if (this._signatureMethod == \"PLAINTEXT\") {\n    hash = key;\n  } else if (this._signatureMethod == \"RSA-SHA1\") {\n    key = this._privateKey || \"\";\n    hash = crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, 'base64');\n  } else {\n    if (crypto.Hmac) {\n      hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n    } else {\n      hash = sha1.HMACSHA1(key, signatureBase);\n    }\n  }\n  return hash;\n};\nexports.OAuth.prototype.NONCE_CHARS = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nexports.OAuth.prototype._getNonce = function (nonceSize) {\n  var result = [];\n  var chars = this.NONCE_CHARS;\n  var char_pos;\n  var nonce_chars_length = chars.length;\n  for (var i = 0; i < nonceSize; i++) {\n    char_pos = Math.floor(Math.random() * nonce_chars_length);\n    result[i] = chars[char_pos];\n  }\n  return result.join('');\n};\nexports.OAuth.prototype._createClient = function (port, hostname, method, path, headers, sslEnabled) {\n  var options = {\n    host: hostname,\n    port: port,\n    path: path,\n    method: method,\n    headers: headers\n  };\n  var httpModel;\n  if (sslEnabled) {\n    httpModel = https;\n  } else {\n    httpModel = http;\n  }\n  return httpModel.request(options);\n};\nexports.OAuth.prototype._prepareParameters = function (oauth_token, oauth_token_secret, method, url, extra_params) {\n  var oauthParameters = {\n    \"oauth_timestamp\": this._getTimestamp(),\n    \"oauth_nonce\": this._getNonce(this._nonceSize),\n    \"oauth_version\": this._version,\n    \"oauth_signature_method\": this._signatureMethod,\n    \"oauth_consumer_key\": this._consumerKey\n  };\n  if (oauth_token) {\n    oauthParameters[\"oauth_token\"] = oauth_token;\n  }\n  var sig;\n  if (this._isEcho) {\n    sig = this._getSignature(\"GET\", this._verifyCredentials, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  } else {\n    if (extra_params) {\n      for (var key in extra_params) {\n        if (extra_params.hasOwnProperty(key)) oauthParameters[key] = extra_params[key];\n      }\n    }\n    var parsedUrl = URL.parse(url, false);\n    if (parsedUrl.query) {\n      var key2;\n      var extraParameters = querystring.parse(parsedUrl.query);\n      for (var key in extraParameters) {\n        var value = extraParameters[key];\n        if (typeof value == \"object\") {\n          // TODO: This probably should be recursive\n          for (key2 in value) {\n            oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n          }\n        } else {\n          oauthParameters[key] = value;\n        }\n      }\n    }\n    sig = this._getSignature(method, url, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n  var orderedParameters = this._sortRequestParams(this._makeArrayOfArgumentsHash(oauthParameters));\n  orderedParameters[orderedParameters.length] = [\"oauth_signature\", sig];\n  return orderedParameters;\n};\nexports.OAuth.prototype._performSecureRequest = function (oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback) {\n  var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n  if (!post_content_type) {\n    post_content_type = \"application/x-www-form-urlencoded\";\n  }\n  var parsedUrl = URL.parse(url, false);\n  if (parsedUrl.protocol == \"http:\" && !parsedUrl.port) parsedUrl.port = 80;\n  if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) parsedUrl.port = 443;\n  var headers = {};\n  var authorization = this._buildAuthorizationHeaders(orderedParameters);\n  if (this._isEcho) {\n    headers[\"X-Verify-Credentials-Authorization\"] = authorization;\n  } else {\n    headers[\"Authorization\"] = authorization;\n  }\n  headers[\"Host\"] = parsedUrl.host;\n  for (var key in this._headers) {\n    if (this._headers.hasOwnProperty(key)) {\n      headers[key] = this._headers[key];\n    }\n  }\n\n  // Filter out any passed extra_params that are really to do with OAuth\n  for (var key in extra_params) {\n    if (this._isParameterNameAnOAuthParameter(key)) {\n      delete extra_params[key];\n    }\n  }\n  if ((method == \"POST\" || method == \"PUT\") && post_body == null && extra_params != null) {\n    // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n    post_body = querystring.stringify(extra_params).replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n  }\n  if (post_body) {\n    if (Buffer.isBuffer(post_body)) {\n      headers[\"Content-length\"] = post_body.length;\n    } else {\n      headers[\"Content-length\"] = Buffer.byteLength(post_body);\n    }\n  } else {\n    headers[\"Content-length\"] = 0;\n  }\n  headers[\"Content-Type\"] = post_content_type;\n  var path;\n  if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n  if (parsedUrl.query) path = parsedUrl.pathname + \"?\" + parsedUrl.query;else path = parsedUrl.pathname;\n  var request;\n  if (parsedUrl.protocol == \"https:\") {\n    request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n  } else {\n    request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n  }\n  var clientOptions = this._clientOptions;\n  if (callback) {\n    var data = \"\";\n    var self = this;\n\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(parsedUrl.hostname);\n    var callbackCalled = false;\n    var passBackControl = function (response) {\n      if (!callbackCalled) {\n        callbackCalled = true;\n        if (response.statusCode >= 200 && response.statusCode <= 299) {\n          callback(null, data, response);\n        } else {\n          // Follow 301 or 302 redirects with Location HTTP header\n          if ((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n            self._performSecureRequest(oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type, callback);\n          } else {\n            callback({\n              statusCode: response.statusCode,\n              data: data\n            }, data, response);\n          }\n        }\n      }\n    };\n    request.on('response', function (response) {\n      response.setEncoding('utf8');\n      response.on('data', function (chunk) {\n        data += chunk;\n      });\n      response.on('end', function () {\n        passBackControl(response);\n      });\n      response.on('close', function () {\n        if (allowEarlyClose) {\n          passBackControl(response);\n        }\n      });\n    });\n    request.on(\"error\", function (err) {\n      if (!callbackCalled) {\n        callbackCalled = true;\n        callback(err);\n      }\n    });\n    if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n      request.write(post_body);\n    }\n    request.end();\n  } else {\n    if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n      request.write(post_body);\n    }\n    return request;\n  }\n  return;\n};\nexports.OAuth.prototype.setClientOptions = function (options) {\n  var key,\n    mergedOptions = {},\n    hasOwnProperty = Object.prototype.hasOwnProperty;\n  for (key in this._defaultClientOptions) {\n    if (!hasOwnProperty.call(options, key)) {\n      mergedOptions[key] = this._defaultClientOptions[key];\n    } else {\n      mergedOptions[key] = options[key];\n    }\n  }\n  this._clientOptions = mergedOptions;\n};\nexports.OAuth.prototype.getOAuthAccessToken = function (oauth_token, oauth_token_secret, oauth_verifier, callback) {\n  var extraParams = {};\n  if (typeof oauth_verifier == \"function\") {\n    callback = oauth_verifier;\n  } else {\n    extraParams.oauth_verifier = oauth_verifier;\n  }\n  this._performSecureRequest(oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function (error, data, response) {\n    if (error) callback(error);else {\n      var results = querystring.parse(data);\n      var oauth_access_token = results[\"oauth_token\"];\n      delete results[\"oauth_token\"];\n      var oauth_access_token_secret = results[\"oauth_token_secret\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_access_token, oauth_access_token_secret, results);\n    }\n  });\n};\n\n// Deprecated\nexports.OAuth.prototype.getProtectedResource = function (url, method, oauth_token, oauth_token_secret, callback) {\n  this._performSecureRequest(oauth_token, oauth_token_secret, method, url, null, \"\", null, callback);\n};\nexports.OAuth.prototype[\"delete\"] = function (url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest(oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype.get = function (url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest(oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype._putOrPost = function (method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  var extra_params = null;\n  if (typeof post_content_type == \"function\") {\n    callback = post_content_type;\n    post_content_type = null;\n  }\n  if (typeof post_body != \"string\" && !Buffer.isBuffer(post_body)) {\n    post_content_type = \"application/x-www-form-urlencoded\";\n    extra_params = post_body;\n    post_body = null;\n  }\n  return this._performSecureRequest(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.put = function (url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.post = function (url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\n\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/\nexports.OAuth.prototype.getOAuthRequestToken = function (extraParams, callback) {\n  if (typeof extraParams == \"function\") {\n    callback = extraParams;\n    extraParams = {};\n  }\n  // Callbacks are 1.0A related\n  if (this._authorize_callback) {\n    extraParams[\"oauth_callback\"] = this._authorize_callback;\n  }\n  this._performSecureRequest(null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function (error, data, response) {\n    if (error) callback(error);else {\n      var results = querystring.parse(data);\n      var oauth_token = results[\"oauth_token\"];\n      var oauth_token_secret = results[\"oauth_token_secret\"];\n      delete results[\"oauth_token\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_token, oauth_token_secret, results);\n    }\n  });\n};\nexports.OAuth.prototype.signUrl = function (url, oauth_token, oauth_token_secret, method) {\n  if (method === undefined) {\n    var method = \"GET\";\n  }\n  var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  var parsedUrl = URL.parse(url, false);\n  var query = \"\";\n  for (var i = 0; i < orderedParameters.length; i++) {\n    query += orderedParameters[i][0] + \"=\" + this._encodeData(orderedParameters[i][1]) + \"&\";\n  }\n  query = query.substring(0, query.length - 1);\n  return parsedUrl.protocol + \"//\" + parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\nexports.OAuth.prototype.authHeader = function (url, oauth_token, oauth_token_secret, method) {\n  if (method === undefined) {\n    var method = \"GET\";\n  }\n  var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  return this._buildAuthorizationHeaders(orderedParameters);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring = __webpack_require__(/*! querystring */ \"querystring\"),\n  crypto = __webpack_require__(/*! crypto */ \"crypto\"),\n  https = __webpack_require__(/*! https */ \"https\"),\n  http = __webpack_require__(/*! http */ \"http\"),\n  URL = __webpack_require__(/*! url */ \"url\"),\n  OAuthUtils = __webpack_require__(/*! ./_utils */ \"(ssr)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth2 = function (clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n  this._clientId = clientId;\n  this._clientSecret = clientSecret;\n  this._baseSite = baseSite;\n  this._authorizeUrl = authorizePath || \"/oauth/authorize\";\n  this._accessTokenUrl = accessTokenPath || \"/oauth/access_token\";\n  this._accessTokenName = \"access_token\";\n  this._authMethod = \"Bearer\";\n  this._customHeaders = customHeaders || {};\n  this._useAuthorizationHeaderForGET = false;\n\n  //our agent\n  this._agent = undefined;\n};\n\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function (agent) {\n  this._agent = agent;\n};\n\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName = function (name) {\n  this._accessTokenName = name;\n};\n\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function (authMethod) {\n  this._authMethod = authMethod;\n};\n\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function (useIt) {\n  this._useAuthorizationHeaderForGET = useIt;\n};\nexports.OAuth2.prototype._getAccessTokenUrl = function () {\n  return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */\n};\n\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader = function (token) {\n  return this._authMethod + ' ' + token;\n};\nexports.OAuth2.prototype._chooseHttpLibrary = function (parsedUrl) {\n  var http_library = https;\n  // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n  if (parsedUrl.protocol != \"https:\") {\n    http_library = http;\n  }\n  return http_library;\n};\nexports.OAuth2.prototype._request = function (method, url, headers, post_body, access_token, callback) {\n  var parsedUrl = URL.parse(url, true);\n  if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) {\n    parsedUrl.port = 443;\n  }\n  var http_library = this._chooseHttpLibrary(parsedUrl);\n  var realHeaders = {};\n  for (var key in this._customHeaders) {\n    realHeaders[key] = this._customHeaders[key];\n  }\n  if (headers) {\n    for (var key in headers) {\n      realHeaders[key] = headers[key];\n    }\n  }\n  realHeaders['Host'] = parsedUrl.host;\n  if (!realHeaders['User-Agent']) {\n    realHeaders['User-Agent'] = 'Node-oauth';\n  }\n  if (post_body) {\n    if (Buffer.isBuffer(post_body)) {\n      realHeaders[\"Content-Length\"] = post_body.length;\n    } else {\n      realHeaders[\"Content-Length\"] = Buffer.byteLength(post_body);\n    }\n  } else {\n    realHeaders[\"Content-length\"] = 0;\n  }\n  if (access_token && !('Authorization' in realHeaders)) {\n    if (!parsedUrl.query) parsedUrl.query = {};\n    parsedUrl.query[this._accessTokenName] = access_token;\n  }\n  var queryStr = querystring.stringify(parsedUrl.query);\n  if (queryStr) queryStr = \"?\" + queryStr;\n  var options = {\n    host: parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname + queryStr,\n    method: method,\n    headers: realHeaders\n  };\n  this._executeRequest(http_library, options, post_body, callback);\n};\nexports.OAuth2.prototype._executeRequest = function (http_library, options, post_body, callback) {\n  // Some hosts *cough* google appear to close the connection early / send no content-length header\n  // allow this behaviour.\n  var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(options.host);\n  var callbackCalled = false;\n  function passBackControl(response, result) {\n    if (!callbackCalled) {\n      callbackCalled = true;\n      if (!(response.statusCode >= 200 && response.statusCode <= 299) && response.statusCode != 301 && response.statusCode != 302) {\n        callback({\n          statusCode: response.statusCode,\n          data: result\n        });\n      } else {\n        callback(null, result, response);\n      }\n    }\n  }\n  var result = \"\";\n\n  //set the agent on the request options\n  if (this._agent) {\n    options.agent = this._agent;\n  }\n  var request = http_library.request(options);\n  request.on('response', function (response) {\n    response.on(\"data\", function (chunk) {\n      result += chunk;\n    });\n    response.on(\"close\", function (err) {\n      if (allowEarlyClose) {\n        passBackControl(response, result);\n      }\n    });\n    response.addListener(\"end\", function () {\n      passBackControl(response, result);\n    });\n  });\n  request.on('error', function (e) {\n    callbackCalled = true;\n    callback(e);\n  });\n  if ((options.method == 'POST' || options.method == 'PUT') && post_body) {\n    request.write(post_body);\n  }\n  request.end();\n};\nexports.OAuth2.prototype.getAuthorizeUrl = function (params) {\n  var params = params || {};\n  params['client_id'] = this._clientId;\n  return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n};\nexports.OAuth2.prototype.getOAuthAccessToken = function (code, params, callback) {\n  var params = params || {};\n  params['client_id'] = this._clientId;\n  params['client_secret'] = this._clientSecret;\n  var codeParam = params.grant_type === 'refresh_token' ? 'refresh_token' : 'code';\n  params[codeParam] = code;\n  var post_data = querystring.stringify(params);\n  var post_headers = {\n    'Content-Type': 'application/x-www-form-urlencoded'\n  };\n  this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function (error, data, response) {\n    if (error) callback(error);else {\n      var results;\n      try {\n        // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n        // responses should be in JSON\n        results = JSON.parse(data);\n      } catch (e) {\n        // .... However both Facebook + Github currently use rev05 of the spec\n        // and neither seem to specify a content-type correctly in their response headers :(\n        // clients of these services will suffer a *minor* performance cost of the exception\n        // being thrown\n        results = querystring.parse(data);\n      }\n      var access_token = results[\"access_token\"];\n      var refresh_token = results[\"refresh_token\"];\n      delete results[\"refresh_token\"];\n      callback(null, access_token, refresh_token, results); // callback results =-=\n    }\n  });\n};\n\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource = function (url, access_token, callback) {\n  this._request(\"GET\", url, {}, \"\", access_token, callback);\n};\nexports.OAuth2.prototype.get = function (url, access_token, callback) {\n  if (this._useAuthorizationHeaderForGET) {\n    var headers = {\n      'Authorization': this.buildAuthHeader(access_token)\n    };\n    access_token = null;\n  } else {\n    headers = {};\n  }\n  this._request(\"GET\", url, headers, \"\", access_token, callback);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL29hdXRoMi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxXQUFXLEdBQUVDLG1CQUFPLENBQUMsZ0NBQWEsQ0FBQztFQUNuQ0MsTUFBTSxHQUFFRCxtQkFBTyxDQUFDLHNCQUFRLENBQUM7RUFDekJFLEtBQUssR0FBRUYsbUJBQU8sQ0FBQyxvQkFBTyxDQUFDO0VBQ3ZCRyxJQUFJLEdBQUVILG1CQUFPLENBQUMsa0JBQU0sQ0FBQztFQUNyQkksR0FBRyxHQUFFSixtQkFBTyxDQUFDLGdCQUFLLENBQUM7RUFDbkJLLFVBQVUsR0FBRUwsbUJBQU8sQ0FBQywwREFBVSxDQUFDO0FBRW5DTSxjQUFjLEdBQUUsVUFBU0UsUUFBUSxFQUFFQyxZQUFZLEVBQUVDLFFBQVEsRUFBRUMsYUFBYSxFQUFFQyxlQUFlLEVBQUVDLGFBQWEsRUFBRTtFQUN4RyxJQUFJLENBQUNDLFNBQVMsR0FBRU4sUUFBUTtFQUN4QixJQUFJLENBQUNPLGFBQWEsR0FBRU4sWUFBWTtFQUNoQyxJQUFJLENBQUNPLFNBQVMsR0FBRU4sUUFBUTtFQUN4QixJQUFJLENBQUNPLGFBQWEsR0FBRU4sYUFBYSxJQUFJLGtCQUFrQjtFQUN2RCxJQUFJLENBQUNPLGVBQWUsR0FBRU4sZUFBZSxJQUFJLHFCQUFxQjtFQUM5RCxJQUFJLENBQUNPLGdCQUFnQixHQUFFLGNBQWM7RUFDckMsSUFBSSxDQUFDQyxXQUFXLEdBQUUsUUFBUTtFQUMxQixJQUFJLENBQUNDLGNBQWMsR0FBR1IsYUFBYSxJQUFJLENBQUMsQ0FBQztFQUN6QyxJQUFJLENBQUNTLDZCQUE2QixHQUFFLEtBQUs7O0VBRXpDO0VBQ0EsSUFBSSxDQUFDQyxNQUFNLEdBQUdDLFNBQVM7QUFDekIsQ0FBQzs7QUFFRDtBQUNBO0FBQ0FsQixpQ0FBaUMsR0FBRyxVQUFTcUIsS0FBSyxFQUFFO0VBQ2xELElBQUksQ0FBQ0osTUFBTSxHQUFHSSxLQUFLO0FBQ3JCLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBckIsMkNBQTJDLEdBQUUsVUFBV3VCLElBQUksRUFBRztFQUM3RCxJQUFJLENBQUNWLGdCQUFnQixHQUFFVSxJQUFJO0FBQzdCLENBQUM7O0FBRUQ7QUFDQTtBQUNBdkIsc0NBQXNDLEdBQUcsVUFBV3lCLFVBQVUsRUFBRztFQUMvRCxJQUFJLENBQUNYLFdBQVcsR0FBR1csVUFBVTtBQUMvQixDQUFDOztBQUdEO0FBQ0E7QUFDQXpCLHFEQUFxRCxHQUFHLFVBQVMyQixLQUFLLEVBQUU7RUFDdEUsSUFBSSxDQUFDWCw2QkFBNkIsR0FBRVcsS0FBSztBQUMzQyxDQUFDO0FBRUQzQiwyQ0FBMkMsR0FBRSxZQUFXO0VBQ3RELE9BQU8sSUFBSSxDQUFDVSxTQUFTLEdBQUcsSUFBSSxDQUFDRSxlQUFlLENBQUMsQ0FBQztBQUNoRCxDQUFDOztBQUVEO0FBQ0E7QUFDQVosd0NBQXdDLEdBQUUsVUFBUzhCLEtBQUssRUFBRTtFQUN4RCxPQUFPLElBQUksQ0FBQ2hCLFdBQVcsR0FBRyxHQUFHLEdBQUdnQixLQUFLO0FBQ3ZDLENBQUM7QUFFRDlCLDJDQUEyQyxHQUFFLFVBQVVnQyxTQUFTLEVBQUc7RUFDakUsSUFBSUMsWUFBWSxHQUFFckMsS0FBSztFQUN2QjtFQUNBLElBQUlvQyxTQUFTLENBQUNFLFFBQVEsSUFBSSxRQUFRLEVBQUc7SUFDbkNELFlBQVksR0FBRXBDLElBQUk7RUFDcEI7RUFDQSxPQUFPb0MsWUFBWTtBQUNyQixDQUFDO0FBRURqQyxpQ0FBaUMsR0FBRSxVQUFTb0MsTUFBTSxFQUFFQyxHQUFHLEVBQUVDLE9BQU8sRUFBRUMsU0FBUyxFQUFFQyxZQUFZLEVBQUVDLFFBQVEsRUFBRTtFQUVuRyxJQUFJVCxTQUFTLEdBQUVsQyxHQUFHLENBQUM0QyxLQUFLLENBQUVMLEdBQUcsRUFBRSxJQUFLLENBQUM7RUFDckMsSUFBSUwsU0FBUyxDQUFDRSxRQUFRLElBQUksUUFBUSxJQUFJLENBQUNGLFNBQVMsQ0FBQ1csSUFBSSxFQUFHO0lBQ3REWCxTQUFTLENBQUNXLElBQUksR0FBRSxHQUFHO0VBQ3JCO0VBRUEsSUFBSVYsWUFBWSxHQUFFLElBQUksQ0FBQ0Ysa0JBQWtCLENBQUVDLFNBQVUsQ0FBQztFQUd0RCxJQUFJWSxXQUFXLEdBQUUsQ0FBQyxDQUFDO0VBQ25CLEtBQUssSUFBSUMsR0FBRyxJQUFJLElBQUksQ0FBQzlCLGNBQWMsRUFBRztJQUNwQzZCLFdBQVcsQ0FBQ0MsR0FBRyxDQUFDLEdBQUUsSUFBSSxDQUFDOUIsY0FBYyxDQUFDOEIsR0FBRyxDQUFDO0VBQzVDO0VBQ0EsSUFBSVAsT0FBTyxFQUFHO0lBQ1osS0FBSSxJQUFJTyxHQUFHLElBQUlQLE9BQU8sRUFBRTtNQUN0Qk0sV0FBVyxDQUFDQyxHQUFHLENBQUMsR0FBR1AsT0FBTyxDQUFDTyxHQUFHLENBQUM7SUFDakM7RUFDRjtFQUNBRCxXQUFXLENBQUMsTUFBTSxDQUFDLEdBQUVaLFNBQVMsQ0FBQ2MsSUFBSTtFQUVuQyxJQUFJLENBQUNGLFdBQVcsQ0FBQyxZQUFZLENBQUMsRUFBRTtJQUM5QkEsV0FBVyxDQUFDLFlBQVksQ0FBQyxHQUFHLFlBQVk7RUFDMUM7RUFFQSxJQUFJTCxTQUFTLEVBQUc7SUFDWixJQUFLUSxNQUFNLENBQUNDLFFBQVEsQ0FBQ1QsU0FBUyxDQUFDLEVBQUc7TUFDOUJLLFdBQVcsQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFFTCxTQUFTLENBQUNVLE1BQU07SUFDbkQsQ0FBQyxNQUFNO01BQ0hMLFdBQVcsQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFFRyxNQUFNLENBQUNHLFVBQVUsQ0FBQ1gsU0FBUyxDQUFDO0lBQy9EO0VBQ0osQ0FBQyxNQUFNO0lBQ0hLLFdBQVcsQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFFLENBQUM7RUFDcEM7RUFFQSxJQUFJSixZQUFZLElBQUksRUFBRSxlQUFlLElBQUlJLFdBQVcsQ0FBQyxFQUFFO0lBQ3JELElBQUksQ0FBRVosU0FBUyxDQUFDbUIsS0FBSyxFQUFHbkIsU0FBUyxDQUFDbUIsS0FBSyxHQUFFLENBQUMsQ0FBQztJQUMzQ25CLFNBQVMsQ0FBQ21CLEtBQUssQ0FBQyxJQUFJLENBQUN0QyxnQkFBZ0IsQ0FBQyxHQUFFMkIsWUFBWTtFQUN0RDtFQUVBLElBQUlZLFFBQVEsR0FBRTNELFdBQVcsQ0FBQzRELFNBQVMsQ0FBQ3JCLFNBQVMsQ0FBQ21CLEtBQUssQ0FBQztFQUNwRCxJQUFJQyxRQUFRLEVBQUdBLFFBQVEsR0FBRyxHQUFHLEdBQUdBLFFBQVE7RUFDeEMsSUFBSUUsT0FBTyxHQUFHO0lBQ1pSLElBQUksRUFBQ2QsU0FBUyxDQUFDdUIsUUFBUTtJQUN2QlosSUFBSSxFQUFFWCxTQUFTLENBQUNXLElBQUk7SUFDcEJhLElBQUksRUFBRXhCLFNBQVMsQ0FBQ3lCLFFBQVEsR0FBR0wsUUFBUTtJQUNuQ2hCLE1BQU0sRUFBRUEsTUFBTTtJQUNkRSxPQUFPLEVBQUVNO0VBQ1gsQ0FBQztFQUVELElBQUksQ0FBQ2MsZUFBZSxDQUFFekIsWUFBWSxFQUFFcUIsT0FBTyxFQUFFZixTQUFTLEVBQUVFLFFBQVMsQ0FBQztBQUNwRSxDQUFDO0FBRUR6Qyx3Q0FBd0MsR0FBRSxVQUFVaUMsWUFBWSxFQUFFcUIsT0FBTyxFQUFFZixTQUFTLEVBQUVFLFFBQVEsRUFBRztFQUMvRjtFQUNBO0VBQ0EsSUFBSWtCLGVBQWUsR0FBRTVELFVBQVUsQ0FBQzZELGtCQUFrQixDQUFDTixPQUFPLENBQUNSLElBQUksQ0FBQztFQUNoRSxJQUFJZSxjQUFjLEdBQUUsS0FBSztFQUN6QixTQUFTQyxlQUFlQSxDQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRztJQUMzQyxJQUFHLENBQUNILGNBQWMsRUFBRTtNQUNsQkEsY0FBYyxHQUFDLElBQUk7TUFDbkIsSUFBSSxFQUFFRSxRQUFRLENBQUNFLFVBQVUsSUFBSSxHQUFHLElBQUlGLFFBQVEsQ0FBQ0UsVUFBVSxJQUFJLEdBQUcsQ0FBQyxJQUFLRixRQUFRLENBQUNFLFVBQVUsSUFBSSxHQUFJLElBQUtGLFFBQVEsQ0FBQ0UsVUFBVSxJQUFJLEdBQUksRUFBRztRQUNoSXhCLFFBQVEsQ0FBQztVQUFFd0IsVUFBVSxFQUFFRixRQUFRLENBQUNFLFVBQVU7VUFBRUMsSUFBSSxFQUFFRjtRQUFPLENBQUMsQ0FBQztNQUM3RCxDQUFDLE1BQU07UUFDTHZCLFFBQVEsQ0FBQyxJQUFJLEVBQUV1QixNQUFNLEVBQUVELFFBQVEsQ0FBQztNQUNsQztJQUNGO0VBQ0Y7RUFFQSxJQUFJQyxNQUFNLEdBQUUsRUFBRTs7RUFFZDtFQUNBLElBQUksSUFBSSxDQUFDL0MsTUFBTSxFQUFFO0lBQ2ZxQyxPQUFPLENBQUNqQyxLQUFLLEdBQUcsSUFBSSxDQUFDSixNQUFNO0VBQzdCO0VBRUEsSUFBSWtELE9BQU8sR0FBR2xDLFlBQVksQ0FBQ2tDLE9BQU8sQ0FBQ2IsT0FBTyxDQUFDO0VBQzNDYSxPQUFPLENBQUNDLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVUwsUUFBUSxFQUFFO0lBQ3pDQSxRQUFRLENBQUNLLEVBQUUsQ0FBQyxNQUFNLEVBQUUsVUFBVUMsS0FBSyxFQUFFO01BQ25DTCxNQUFNLElBQUdLLEtBQUs7SUFDaEIsQ0FBQyxDQUFDO0lBQ0ZOLFFBQVEsQ0FBQ0ssRUFBRSxDQUFDLE9BQU8sRUFBRSxVQUFVRSxHQUFHLEVBQUU7TUFDbEMsSUFBSVgsZUFBZSxFQUFHO1FBQ3BCRyxlQUFlLENBQUVDLFFBQVEsRUFBRUMsTUFBTyxDQUFDO01BQ3JDO0lBQ0YsQ0FBQyxDQUFDO0lBQ0ZELFFBQVEsQ0FBQ1EsV0FBVyxDQUFDLEtBQUssRUFBRSxZQUFZO01BQ3RDVCxlQUFlLENBQUVDLFFBQVEsRUFBRUMsTUFBTyxDQUFDO0lBQ3JDLENBQUMsQ0FBQztFQUNKLENBQUMsQ0FBQztFQUNGRyxPQUFPLENBQUNDLEVBQUUsQ0FBQyxPQUFPLEVBQUUsVUFBU0ksQ0FBQyxFQUFFO0lBQzlCWCxjQUFjLEdBQUUsSUFBSTtJQUNwQnBCLFFBQVEsQ0FBQytCLENBQUMsQ0FBQztFQUNiLENBQUMsQ0FBQztFQUVGLElBQUksQ0FBQ2xCLE9BQU8sQ0FBQ2xCLE1BQU0sSUFBSSxNQUFNLElBQUlrQixPQUFPLENBQUNsQixNQUFNLElBQUksS0FBSyxLQUFLRyxTQUFTLEVBQUc7SUFDdEU0QixPQUFPLENBQUNNLEtBQUssQ0FBQ2xDLFNBQVMsQ0FBQztFQUMzQjtFQUNBNEIsT0FBTyxDQUFDTyxHQUFHLENBQUMsQ0FBQztBQUNmLENBQUM7QUFFRDFFLHdDQUF3QyxHQUFFLFVBQVU0RSxNQUFNLEVBQUc7RUFDM0QsSUFBSUEsTUFBTSxHQUFFQSxNQUFNLElBQUksQ0FBQyxDQUFDO0VBQ3hCQSxNQUFNLENBQUMsV0FBVyxDQUFDLEdBQUcsSUFBSSxDQUFDcEUsU0FBUztFQUNwQyxPQUFPLElBQUksQ0FBQ0UsU0FBUyxHQUFHLElBQUksQ0FBQ0MsYUFBYSxHQUFHLEdBQUcsR0FBR2xCLFdBQVcsQ0FBQzRELFNBQVMsQ0FBQ3VCLE1BQU0sQ0FBQztBQUNsRixDQUFDO0FBRUQ1RSw0Q0FBNEMsR0FBRSxVQUFTOEUsSUFBSSxFQUFFRixNQUFNLEVBQUVuQyxRQUFRLEVBQUU7RUFDN0UsSUFBSW1DLE1BQU0sR0FBRUEsTUFBTSxJQUFJLENBQUMsQ0FBQztFQUN4QkEsTUFBTSxDQUFDLFdBQVcsQ0FBQyxHQUFHLElBQUksQ0FBQ3BFLFNBQVM7RUFDcENvRSxNQUFNLENBQUMsZUFBZSxDQUFDLEdBQUcsSUFBSSxDQUFDbkUsYUFBYTtFQUM1QyxJQUFJc0UsU0FBUyxHQUFJSCxNQUFNLENBQUNJLFVBQVUsS0FBSyxlQUFlLEdBQUksZUFBZSxHQUFHLE1BQU07RUFDbEZKLE1BQU0sQ0FBQ0csU0FBUyxDQUFDLEdBQUVELElBQUk7RUFFdkIsSUFBSUcsU0FBUyxHQUFFeEYsV0FBVyxDQUFDNEQsU0FBUyxDQUFFdUIsTUFBTyxDQUFDO0VBQzlDLElBQUlNLFlBQVksR0FBRTtJQUNiLGNBQWMsRUFBRTtFQUNwQixDQUFDO0VBR0YsSUFBSSxDQUFDL0MsUUFBUSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUNQLGtCQUFrQixDQUFDLENBQUMsRUFBRXNELFlBQVksRUFBRUQsU0FBUyxFQUFFLElBQUksRUFBRSxVQUFTRSxLQUFLLEVBQUVqQixJQUFJLEVBQUVILFFBQVEsRUFBRTtJQUM5RyxJQUFJb0IsS0FBSyxFQUFJMUMsUUFBUSxDQUFDMEMsS0FBSyxDQUFDLENBQUMsS0FDeEI7TUFDSCxJQUFJQyxPQUFPO01BQ1gsSUFBSTtRQUNGO1FBQ0E7UUFDQUEsT0FBTyxHQUFFQyxJQUFJLENBQUMzQyxLQUFLLENBQUV3QixJQUFLLENBQUM7TUFDN0IsQ0FBQyxDQUNELE9BQU1NLENBQUMsRUFBRTtRQUNQO1FBQ0E7UUFDQTtRQUNBO1FBQ0FZLE9BQU8sR0FBRTNGLFdBQVcsQ0FBQ2lELEtBQUssQ0FBRXdCLElBQUssQ0FBQztNQUNwQztNQUNBLElBQUkxQixZQUFZLEdBQUU0QyxPQUFPLENBQUMsY0FBYyxDQUFDO01BQ3pDLElBQUlFLGFBQWEsR0FBRUYsT0FBTyxDQUFDLGVBQWUsQ0FBQztNQUMzQyxPQUFPQSxPQUFPLENBQUMsZUFBZSxDQUFDO01BQy9CM0MsUUFBUSxDQUFDLElBQUksRUFBRUQsWUFBWSxFQUFFOEMsYUFBYSxFQUFFRixPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQ3hEO0VBQ0YsQ0FBQyxDQUFDO0FBQ0osQ0FBQzs7QUFFRDtBQUNBcEYsNkNBQTZDLEdBQUUsVUFBU3FDLEdBQUcsRUFBRUcsWUFBWSxFQUFFQyxRQUFRLEVBQUU7RUFDbkYsSUFBSSxDQUFDTixRQUFRLENBQUMsS0FBSyxFQUFFRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFRyxZQUFZLEVBQUVDLFFBQVMsQ0FBQztBQUM1RCxDQUFDO0FBRUR6Qyw0QkFBNEIsR0FBRSxVQUFTcUMsR0FBRyxFQUFFRyxZQUFZLEVBQUVDLFFBQVEsRUFBRTtFQUNsRSxJQUFJLElBQUksQ0FBQ3pCLDZCQUE2QixFQUFHO0lBQ3ZDLElBQUlzQixPQUFPLEdBQUU7TUFBQyxlQUFlLEVBQUUsSUFBSSxDQUFDVCxlQUFlLENBQUNXLFlBQVk7SUFBRSxDQUFDO0lBQ25FQSxZQUFZLEdBQUUsSUFBSTtFQUNwQixDQUFDLE1BQ0k7SUFDSEYsT0FBTyxHQUFFLENBQUMsQ0FBQztFQUNiO0VBQ0EsSUFBSSxDQUFDSCxRQUFRLENBQUMsS0FBSyxFQUFFRSxHQUFHLEVBQUVDLE9BQU8sRUFBRSxFQUFFLEVBQUVFLFlBQVksRUFBRUMsUUFBUyxDQUFDO0FBQ2pFLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvYXV0aFxcbGliXFxvYXV0aDIuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHF1ZXJ5c3RyaW5nPSByZXF1aXJlKCdxdWVyeXN0cmluZycpLFxuICAgIGNyeXB0bz0gcmVxdWlyZSgnY3J5cHRvJyksXG4gICAgaHR0cHM9IHJlcXVpcmUoJ2h0dHBzJyksXG4gICAgaHR0cD0gcmVxdWlyZSgnaHR0cCcpLFxuICAgIFVSTD0gcmVxdWlyZSgndXJsJyksXG4gICAgT0F1dGhVdGlscz0gcmVxdWlyZSgnLi9fdXRpbHMnKTtcblxuZXhwb3J0cy5PQXV0aDI9IGZ1bmN0aW9uKGNsaWVudElkLCBjbGllbnRTZWNyZXQsIGJhc2VTaXRlLCBhdXRob3JpemVQYXRoLCBhY2Nlc3NUb2tlblBhdGgsIGN1c3RvbUhlYWRlcnMpIHtcbiAgdGhpcy5fY2xpZW50SWQ9IGNsaWVudElkO1xuICB0aGlzLl9jbGllbnRTZWNyZXQ9IGNsaWVudFNlY3JldDtcbiAgdGhpcy5fYmFzZVNpdGU9IGJhc2VTaXRlO1xuICB0aGlzLl9hdXRob3JpemVVcmw9IGF1dGhvcml6ZVBhdGggfHwgXCIvb2F1dGgvYXV0aG9yaXplXCI7XG4gIHRoaXMuX2FjY2Vzc1Rva2VuVXJsPSBhY2Nlc3NUb2tlblBhdGggfHwgXCIvb2F1dGgvYWNjZXNzX3Rva2VuXCI7XG4gIHRoaXMuX2FjY2Vzc1Rva2VuTmFtZT0gXCJhY2Nlc3NfdG9rZW5cIjtcbiAgdGhpcy5fYXV0aE1ldGhvZD0gXCJCZWFyZXJcIjtcbiAgdGhpcy5fY3VzdG9tSGVhZGVycyA9IGN1c3RvbUhlYWRlcnMgfHwge307XG4gIHRoaXMuX3VzZUF1dGhvcml6YXRpb25IZWFkZXJGb3JHRVQ9IGZhbHNlO1xuXG4gIC8vb3VyIGFnZW50XG4gIHRoaXMuX2FnZW50ID0gdW5kZWZpbmVkO1xufTtcblxuLy8gQWxsb3dzIHlvdSB0byBzZXQgYW4gYWdlbnQgdG8gdXNlIGluc3RlYWQgb2YgdGhlIGRlZmF1bHQgSFRUUCBvclxuLy8gSFRUUFMgYWdlbnRzLiBVc2VmdWwgd2hlbiBkZWFsaW5nIHdpdGggeW91ciBvd24gY2VydGlmaWNhdGVzLlxuZXhwb3J0cy5PQXV0aDIucHJvdG90eXBlLnNldEFnZW50ID0gZnVuY3Rpb24oYWdlbnQpIHtcbiAgdGhpcy5fYWdlbnQgPSBhZ2VudDtcbn07XG5cbi8vIFRoaXMgJ2hhY2snIG1ldGhvZCBpcyByZXF1aXJlZCBmb3Igc2l0ZXMgdGhhdCBkb24ndCB1c2Vcbi8vICdhY2Nlc3NfdG9rZW4nIGFzIHRoZSBuYW1lIG9mIHRoZSBhY2Nlc3MgdG9rZW4gKGZvciByZXF1ZXN0cykuXG4vLyAoIGh0dHA6Ly90b29scy5pZXRmLm9yZy9odG1sL2RyYWZ0LWlldGYtb2F1dGgtdjItMTYjc2VjdGlvbi03IClcbi8vIGl0IGlzbid0IGNsZWFyIHdoYXQgdGhlIGNvcnJlY3QgdmFsdWUgc2hvdWxkIGJlIGF0bSwgc28gYWxsb3dpbmdcbi8vIGZvciBzcGVjaWZpYyAodGVtcG9yYXJ5Pykgb3ZlcnJpZGUgZm9yIG5vdy5cbmV4cG9ydHMuT0F1dGgyLnByb3RvdHlwZS5zZXRBY2Nlc3NUb2tlbk5hbWU9IGZ1bmN0aW9uICggbmFtZSApIHtcbiAgdGhpcy5fYWNjZXNzVG9rZW5OYW1lPSBuYW1lO1xufVxuXG4vLyBTZXRzIHRoZSBhdXRob3JpemF0aW9uIG1ldGhvZCBmb3IgQXV0aG9yaXphdGlvbiBoZWFkZXIuXG4vLyBlLmcuIEF1dGhvcml6YXRpb246IEJlYXJlciA8dG9rZW4+ICAjIFwiQmVhcmVyXCIgaXMgdGhlIGF1dGhvcml6YXRpb24gbWV0aG9kLlxuZXhwb3J0cy5PQXV0aDIucHJvdG90eXBlLnNldEF1dGhNZXRob2QgPSBmdW5jdGlvbiAoIGF1dGhNZXRob2QgKSB7XG4gIHRoaXMuX2F1dGhNZXRob2QgPSBhdXRoTWV0aG9kO1xufTtcblxuXG4vLyBJZiB5b3UgdXNlIHRoZSBPQXV0aDIgZXhwb3NlZCAnZ2V0JyBtZXRob2QgKGFuZCBkb24ndCBjb25zdHJ1Y3QgeW91ciBvd24gX3JlcXVlc3QgY2FsbCApXG4vLyB0aGlzIHdpbGwgc3BlY2lmeSB3aGV0aGVyIHRvIHVzZSBhbiAnQXV0aG9yaXplJyBoZWFkZXIgaW5zdGVhZCBvZiBwYXNzaW5nIHRoZSBhY2Nlc3NfdG9rZW4gYXMgYSBxdWVyeSBwYXJhbWV0ZXJcbmV4cG9ydHMuT0F1dGgyLnByb3RvdHlwZS51c2VBdXRob3JpemF0aW9uSGVhZGVyZm9yR0VUID0gZnVuY3Rpb24odXNlSXQpIHtcbiAgdGhpcy5fdXNlQXV0aG9yaXphdGlvbkhlYWRlckZvckdFVD0gdXNlSXQ7XG59XG5cbmV4cG9ydHMuT0F1dGgyLnByb3RvdHlwZS5fZ2V0QWNjZXNzVG9rZW5Vcmw9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gdGhpcy5fYmFzZVNpdGUgKyB0aGlzLl9hY2Nlc3NUb2tlblVybDsgLyogKyBcIj9cIiArIHF1ZXJ5c3RyaW5nLnN0cmluZ2lmeShwYXJhbXMpOyAqL1xufVxuXG4vLyBCdWlsZCB0aGUgYXV0aG9yaXphdGlvbiBoZWFkZXIuIEluIHBhcnRpY3VsYXIsIGJ1aWxkIHRoZSBwYXJ0IGFmdGVyIHRoZSBjb2xvbi5cbi8vIGUuZy4gQXV0aG9yaXphdGlvbjogQmVhcmVyIDx0b2tlbj4gICMgQnVpbGQgXCJCZWFyZXIgPHRva2VuPlwiXG5leHBvcnRzLk9BdXRoMi5wcm90b3R5cGUuYnVpbGRBdXRoSGVhZGVyPSBmdW5jdGlvbih0b2tlbikge1xuICByZXR1cm4gdGhpcy5fYXV0aE1ldGhvZCArICcgJyArIHRva2VuO1xufTtcblxuZXhwb3J0cy5PQXV0aDIucHJvdG90eXBlLl9jaG9vc2VIdHRwTGlicmFyeT0gZnVuY3Rpb24oIHBhcnNlZFVybCApIHtcbiAgdmFyIGh0dHBfbGlicmFyeT0gaHR0cHM7XG4gIC8vIEFzIHRoaXMgaXMgT0FVdGgyLCB3ZSAqYXNzdW1lKiBodHRwcyB1bmxlc3MgdG9sZCBleHBsaWNpdGx5IG90aGVyd2lzZS5cbiAgaWYoIHBhcnNlZFVybC5wcm90b2NvbCAhPSBcImh0dHBzOlwiICkge1xuICAgIGh0dHBfbGlicmFyeT0gaHR0cDtcbiAgfVxuICByZXR1cm4gaHR0cF9saWJyYXJ5O1xufTtcblxuZXhwb3J0cy5PQXV0aDIucHJvdG90eXBlLl9yZXF1ZXN0PSBmdW5jdGlvbihtZXRob2QsIHVybCwgaGVhZGVycywgcG9zdF9ib2R5LCBhY2Nlc3NfdG9rZW4sIGNhbGxiYWNrKSB7XG5cbiAgdmFyIHBhcnNlZFVybD0gVVJMLnBhcnNlKCB1cmwsIHRydWUgKTtcbiAgaWYoIHBhcnNlZFVybC5wcm90b2NvbCA9PSBcImh0dHBzOlwiICYmICFwYXJzZWRVcmwucG9ydCApIHtcbiAgICBwYXJzZWRVcmwucG9ydD0gNDQzO1xuICB9XG5cbiAgdmFyIGh0dHBfbGlicmFyeT0gdGhpcy5fY2hvb3NlSHR0cExpYnJhcnkoIHBhcnNlZFVybCApO1xuXG5cbiAgdmFyIHJlYWxIZWFkZXJzPSB7fTtcbiAgZm9yKCB2YXIga2V5IGluIHRoaXMuX2N1c3RvbUhlYWRlcnMgKSB7XG4gICAgcmVhbEhlYWRlcnNba2V5XT0gdGhpcy5fY3VzdG9tSGVhZGVyc1trZXldO1xuICB9XG4gIGlmKCBoZWFkZXJzICkge1xuICAgIGZvcih2YXIga2V5IGluIGhlYWRlcnMpIHtcbiAgICAgIHJlYWxIZWFkZXJzW2tleV0gPSBoZWFkZXJzW2tleV07XG4gICAgfVxuICB9XG4gIHJlYWxIZWFkZXJzWydIb3N0J109IHBhcnNlZFVybC5ob3N0O1xuXG4gIGlmICghcmVhbEhlYWRlcnNbJ1VzZXItQWdlbnQnXSkge1xuICAgIHJlYWxIZWFkZXJzWydVc2VyLUFnZW50J10gPSAnTm9kZS1vYXV0aCc7XG4gIH1cblxuICBpZiggcG9zdF9ib2R5ICkge1xuICAgICAgaWYgKCBCdWZmZXIuaXNCdWZmZXIocG9zdF9ib2R5KSApIHtcbiAgICAgICAgICByZWFsSGVhZGVyc1tcIkNvbnRlbnQtTGVuZ3RoXCJdPSBwb3N0X2JvZHkubGVuZ3RoO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZWFsSGVhZGVyc1tcIkNvbnRlbnQtTGVuZ3RoXCJdPSBCdWZmZXIuYnl0ZUxlbmd0aChwb3N0X2JvZHkpO1xuICAgICAgfVxuICB9IGVsc2Uge1xuICAgICAgcmVhbEhlYWRlcnNbXCJDb250ZW50LWxlbmd0aFwiXT0gMDtcbiAgfVxuXG4gIGlmKCBhY2Nlc3NfdG9rZW4gJiYgISgnQXV0aG9yaXphdGlvbicgaW4gcmVhbEhlYWRlcnMpKSB7XG4gICAgaWYoICEgcGFyc2VkVXJsLnF1ZXJ5ICkgcGFyc2VkVXJsLnF1ZXJ5PSB7fTtcbiAgICBwYXJzZWRVcmwucXVlcnlbdGhpcy5fYWNjZXNzVG9rZW5OYW1lXT0gYWNjZXNzX3Rva2VuO1xuICB9XG5cbiAgdmFyIHF1ZXJ5U3RyPSBxdWVyeXN0cmluZy5zdHJpbmdpZnkocGFyc2VkVXJsLnF1ZXJ5KTtcbiAgaWYoIHF1ZXJ5U3RyICkgcXVlcnlTdHI9ICBcIj9cIiArIHF1ZXJ5U3RyO1xuICB2YXIgb3B0aW9ucyA9IHtcbiAgICBob3N0OnBhcnNlZFVybC5ob3N0bmFtZSxcbiAgICBwb3J0OiBwYXJzZWRVcmwucG9ydCxcbiAgICBwYXRoOiBwYXJzZWRVcmwucGF0aG5hbWUgKyBxdWVyeVN0cixcbiAgICBtZXRob2Q6IG1ldGhvZCxcbiAgICBoZWFkZXJzOiByZWFsSGVhZGVyc1xuICB9O1xuXG4gIHRoaXMuX2V4ZWN1dGVSZXF1ZXN0KCBodHRwX2xpYnJhcnksIG9wdGlvbnMsIHBvc3RfYm9keSwgY2FsbGJhY2sgKTtcbn1cblxuZXhwb3J0cy5PQXV0aDIucHJvdG90eXBlLl9leGVjdXRlUmVxdWVzdD0gZnVuY3Rpb24oIGh0dHBfbGlicmFyeSwgb3B0aW9ucywgcG9zdF9ib2R5LCBjYWxsYmFjayApIHtcbiAgLy8gU29tZSBob3N0cyAqY291Z2gqIGdvb2dsZSBhcHBlYXIgdG8gY2xvc2UgdGhlIGNvbm5lY3Rpb24gZWFybHkgLyBzZW5kIG5vIGNvbnRlbnQtbGVuZ3RoIGhlYWRlclxuICAvLyBhbGxvdyB0aGlzIGJlaGF2aW91ci5cbiAgdmFyIGFsbG93RWFybHlDbG9zZT0gT0F1dGhVdGlscy5pc0FuRWFybHlDbG9zZUhvc3Qob3B0aW9ucy5ob3N0KTtcbiAgdmFyIGNhbGxiYWNrQ2FsbGVkPSBmYWxzZTtcbiAgZnVuY3Rpb24gcGFzc0JhY2tDb250cm9sKCByZXNwb25zZSwgcmVzdWx0ICkge1xuICAgIGlmKCFjYWxsYmFja0NhbGxlZCkge1xuICAgICAgY2FsbGJhY2tDYWxsZWQ9dHJ1ZTtcbiAgICAgIGlmKCAhKHJlc3BvbnNlLnN0YXR1c0NvZGUgPj0gMjAwICYmIHJlc3BvbnNlLnN0YXR1c0NvZGUgPD0gMjk5KSAmJiAocmVzcG9uc2Uuc3RhdHVzQ29kZSAhPSAzMDEpICYmIChyZXNwb25zZS5zdGF0dXNDb2RlICE9IDMwMikgKSB7XG4gICAgICAgIGNhbGxiYWNrKHsgc3RhdHVzQ29kZTogcmVzcG9uc2Uuc3RhdHVzQ29kZSwgZGF0YTogcmVzdWx0IH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY2FsbGJhY2sobnVsbCwgcmVzdWx0LCByZXNwb25zZSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgdmFyIHJlc3VsdD0gXCJcIjtcblxuICAvL3NldCB0aGUgYWdlbnQgb24gdGhlIHJlcXVlc3Qgb3B0aW9uc1xuICBpZiAodGhpcy5fYWdlbnQpIHtcbiAgICBvcHRpb25zLmFnZW50ID0gdGhpcy5fYWdlbnQ7XG4gIH1cblxuICB2YXIgcmVxdWVzdCA9IGh0dHBfbGlicmFyeS5yZXF1ZXN0KG9wdGlvbnMpO1xuICByZXF1ZXN0Lm9uKCdyZXNwb25zZScsIGZ1bmN0aW9uIChyZXNwb25zZSkge1xuICAgIHJlc3BvbnNlLm9uKFwiZGF0YVwiLCBmdW5jdGlvbiAoY2h1bmspIHtcbiAgICAgIHJlc3VsdCs9IGNodW5rXG4gICAgfSk7XG4gICAgcmVzcG9uc2Uub24oXCJjbG9zZVwiLCBmdW5jdGlvbiAoZXJyKSB7XG4gICAgICBpZiggYWxsb3dFYXJseUNsb3NlICkge1xuICAgICAgICBwYXNzQmFja0NvbnRyb2woIHJlc3BvbnNlLCByZXN1bHQgKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICByZXNwb25zZS5hZGRMaXN0ZW5lcihcImVuZFwiLCBmdW5jdGlvbiAoKSB7XG4gICAgICBwYXNzQmFja0NvbnRyb2woIHJlc3BvbnNlLCByZXN1bHQgKTtcbiAgICB9KTtcbiAgfSk7XG4gIHJlcXVlc3Qub24oJ2Vycm9yJywgZnVuY3Rpb24oZSkge1xuICAgIGNhbGxiYWNrQ2FsbGVkPSB0cnVlO1xuICAgIGNhbGxiYWNrKGUpO1xuICB9KTtcblxuICBpZiggKG9wdGlvbnMubWV0aG9kID09ICdQT1NUJyB8fCBvcHRpb25zLm1ldGhvZCA9PSAnUFVUJykgJiYgcG9zdF9ib2R5ICkge1xuICAgICByZXF1ZXN0LndyaXRlKHBvc3RfYm9keSk7XG4gIH1cbiAgcmVxdWVzdC5lbmQoKTtcbn1cblxuZXhwb3J0cy5PQXV0aDIucHJvdG90eXBlLmdldEF1dGhvcml6ZVVybD0gZnVuY3Rpb24oIHBhcmFtcyApIHtcbiAgdmFyIHBhcmFtcz0gcGFyYW1zIHx8IHt9O1xuICBwYXJhbXNbJ2NsaWVudF9pZCddID0gdGhpcy5fY2xpZW50SWQ7XG4gIHJldHVybiB0aGlzLl9iYXNlU2l0ZSArIHRoaXMuX2F1dGhvcml6ZVVybCArIFwiP1wiICsgcXVlcnlzdHJpbmcuc3RyaW5naWZ5KHBhcmFtcyk7XG59XG5cbmV4cG9ydHMuT0F1dGgyLnByb3RvdHlwZS5nZXRPQXV0aEFjY2Vzc1Rva2VuPSBmdW5jdGlvbihjb2RlLCBwYXJhbXMsIGNhbGxiYWNrKSB7XG4gIHZhciBwYXJhbXM9IHBhcmFtcyB8fCB7fTtcbiAgcGFyYW1zWydjbGllbnRfaWQnXSA9IHRoaXMuX2NsaWVudElkO1xuICBwYXJhbXNbJ2NsaWVudF9zZWNyZXQnXSA9IHRoaXMuX2NsaWVudFNlY3JldDtcbiAgdmFyIGNvZGVQYXJhbSA9IChwYXJhbXMuZ3JhbnRfdHlwZSA9PT0gJ3JlZnJlc2hfdG9rZW4nKSA/ICdyZWZyZXNoX3Rva2VuJyA6ICdjb2RlJztcbiAgcGFyYW1zW2NvZGVQYXJhbV09IGNvZGU7XG5cbiAgdmFyIHBvc3RfZGF0YT0gcXVlcnlzdHJpbmcuc3RyaW5naWZ5KCBwYXJhbXMgKTtcbiAgdmFyIHBvc3RfaGVhZGVycz0ge1xuICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24veC13d3ctZm9ybS11cmxlbmNvZGVkJ1xuICAgfTtcblxuXG4gIHRoaXMuX3JlcXVlc3QoXCJQT1NUXCIsIHRoaXMuX2dldEFjY2Vzc1Rva2VuVXJsKCksIHBvc3RfaGVhZGVycywgcG9zdF9kYXRhLCBudWxsLCBmdW5jdGlvbihlcnJvciwgZGF0YSwgcmVzcG9uc2UpIHtcbiAgICBpZiggZXJyb3IgKSAgY2FsbGJhY2soZXJyb3IpO1xuICAgIGVsc2Uge1xuICAgICAgdmFyIHJlc3VsdHM7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBBcyBvZiBodHRwOi8vdG9vbHMuaWV0Zi5vcmcvaHRtbC9kcmFmdC1pZXRmLW9hdXRoLXYyLTA3XG4gICAgICAgIC8vIHJlc3BvbnNlcyBzaG91bGQgYmUgaW4gSlNPTlxuICAgICAgICByZXN1bHRzPSBKU09OLnBhcnNlKCBkYXRhICk7XG4gICAgICB9XG4gICAgICBjYXRjaChlKSB7XG4gICAgICAgIC8vIC4uLi4gSG93ZXZlciBib3RoIEZhY2Vib29rICsgR2l0aHViIGN1cnJlbnRseSB1c2UgcmV2MDUgb2YgdGhlIHNwZWNcbiAgICAgICAgLy8gYW5kIG5laXRoZXIgc2VlbSB0byBzcGVjaWZ5IGEgY29udGVudC10eXBlIGNvcnJlY3RseSBpbiB0aGVpciByZXNwb25zZSBoZWFkZXJzIDooXG4gICAgICAgIC8vIGNsaWVudHMgb2YgdGhlc2Ugc2VydmljZXMgd2lsbCBzdWZmZXIgYSAqbWlub3IqIHBlcmZvcm1hbmNlIGNvc3Qgb2YgdGhlIGV4Y2VwdGlvblxuICAgICAgICAvLyBiZWluZyB0aHJvd25cbiAgICAgICAgcmVzdWx0cz0gcXVlcnlzdHJpbmcucGFyc2UoIGRhdGEgKTtcbiAgICAgIH1cbiAgICAgIHZhciBhY2Nlc3NfdG9rZW49IHJlc3VsdHNbXCJhY2Nlc3NfdG9rZW5cIl07XG4gICAgICB2YXIgcmVmcmVzaF90b2tlbj0gcmVzdWx0c1tcInJlZnJlc2hfdG9rZW5cIl07XG4gICAgICBkZWxldGUgcmVzdWx0c1tcInJlZnJlc2hfdG9rZW5cIl07XG4gICAgICBjYWxsYmFjayhudWxsLCBhY2Nlc3NfdG9rZW4sIHJlZnJlc2hfdG9rZW4sIHJlc3VsdHMpOyAvLyBjYWxsYmFjayByZXN1bHRzID0tPVxuICAgIH1cbiAgfSk7XG59XG5cbi8vIERlcHJlY2F0ZWRcbmV4cG9ydHMuT0F1dGgyLnByb3RvdHlwZS5nZXRQcm90ZWN0ZWRSZXNvdXJjZT0gZnVuY3Rpb24odXJsLCBhY2Nlc3NfdG9rZW4sIGNhbGxiYWNrKSB7XG4gIHRoaXMuX3JlcXVlc3QoXCJHRVRcIiwgdXJsLCB7fSwgXCJcIiwgYWNjZXNzX3Rva2VuLCBjYWxsYmFjayApO1xufVxuXG5leHBvcnRzLk9BdXRoMi5wcm90b3R5cGUuZ2V0PSBmdW5jdGlvbih1cmwsIGFjY2Vzc190b2tlbiwgY2FsbGJhY2spIHtcbiAgaWYoIHRoaXMuX3VzZUF1dGhvcml6YXRpb25IZWFkZXJGb3JHRVQgKSB7XG4gICAgdmFyIGhlYWRlcnM9IHsnQXV0aG9yaXphdGlvbic6IHRoaXMuYnVpbGRBdXRoSGVhZGVyKGFjY2Vzc190b2tlbikgfVxuICAgIGFjY2Vzc190b2tlbj0gbnVsbDtcbiAgfVxuICBlbHNlIHtcbiAgICBoZWFkZXJzPSB7fTtcbiAgfVxuICB0aGlzLl9yZXF1ZXN0KFwiR0VUXCIsIHVybCwgaGVhZGVycywgXCJcIiwgYWNjZXNzX3Rva2VuLCBjYWxsYmFjayApO1xufVxuIl0sIm5hbWVzIjpbInF1ZXJ5c3RyaW5nIiwicmVxdWlyZSIsImNyeXB0byIsImh0dHBzIiwiaHR0cCIsIlVSTCIsIk9BdXRoVXRpbHMiLCJleHBvcnRzIiwiT0F1dGgyIiwiY2xpZW50SWQiLCJjbGllbnRTZWNyZXQiLCJiYXNlU2l0ZSIsImF1dGhvcml6ZVBhdGgiLCJhY2Nlc3NUb2tlblBhdGgiLCJjdXN0b21IZWFkZXJzIiwiX2NsaWVudElkIiwiX2NsaWVudFNlY3JldCIsIl9iYXNlU2l0ZSIsIl9hdXRob3JpemVVcmwiLCJfYWNjZXNzVG9rZW5VcmwiLCJfYWNjZXNzVG9rZW5OYW1lIiwiX2F1dGhNZXRob2QiLCJfY3VzdG9tSGVhZGVycyIsIl91c2VBdXRob3JpemF0aW9uSGVhZGVyRm9yR0VUIiwiX2FnZW50IiwidW5kZWZpbmVkIiwicHJvdG90eXBlIiwic2V0QWdlbnQiLCJhZ2VudCIsInNldEFjY2Vzc1Rva2VuTmFtZSIsIm5hbWUiLCJzZXRBdXRoTWV0aG9kIiwiYXV0aE1ldGhvZCIsInVzZUF1dGhvcml6YXRpb25IZWFkZXJmb3JHRVQiLCJ1c2VJdCIsIl9nZXRBY2Nlc3NUb2tlblVybCIsImJ1aWxkQXV0aEhlYWRlciIsInRva2VuIiwiX2Nob29zZUh0dHBMaWJyYXJ5IiwicGFyc2VkVXJsIiwiaHR0cF9saWJyYXJ5IiwicHJvdG9jb2wiLCJfcmVxdWVzdCIsIm1ldGhvZCIsInVybCIsImhlYWRlcnMiLCJwb3N0X2JvZHkiLCJhY2Nlc3NfdG9rZW4iLCJjYWxsYmFjayIsInBhcnNlIiwicG9ydCIsInJlYWxIZWFkZXJzIiwia2V5IiwiaG9zdCIsIkJ1ZmZlciIsImlzQnVmZmVyIiwibGVuZ3RoIiwiYnl0ZUxlbmd0aCIsInF1ZXJ5IiwicXVlcnlTdHIiLCJzdHJpbmdpZnkiLCJvcHRpb25zIiwiaG9zdG5hbWUiLCJwYXRoIiwicGF0aG5hbWUiLCJfZXhlY3V0ZVJlcXVlc3QiLCJhbGxvd0Vhcmx5Q2xvc2UiLCJpc0FuRWFybHlDbG9zZUhvc3QiLCJjYWxsYmFja0NhbGxlZCIsInBhc3NCYWNrQ29udHJvbCIsInJlc3BvbnNlIiwicmVzdWx0Iiwic3RhdHVzQ29kZSIsImRhdGEiLCJyZXF1ZXN0Iiwib24iLCJjaHVuayIsImVyciIsImFkZExpc3RlbmVyIiwiZSIsIndyaXRlIiwiZW5kIiwiZ2V0QXV0aG9yaXplVXJsIiwicGFyYW1zIiwiZ2V0T0F1dGhBY2Nlc3NUb2tlbiIsImNvZGUiLCJjb2RlUGFyYW0iLCJncmFudF90eXBlIiwicG9zdF9kYXRhIiwicG9zdF9oZWFkZXJzIiwiZXJyb3IiLCJyZXN1bHRzIiwiSlNPTiIsInJlZnJlc2hfdG9rZW4iLCJnZXRQcm90ZWN0ZWRSZXNvdXJjZSIsImdldCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */\n\n/*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */\nvar hexcase = 1; /* hex output format. 0 - lowercase; 1 - uppercase        */\nvar b64pad = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n\n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */\nfunction hex_sha1(s) {\n  return rstr2hex(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction b64_sha1(s) {\n  return rstr2b64(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction any_sha1(s, e) {\n  return rstr2any(rstr_sha1(str2rstr_utf8(s)), e);\n}\nfunction hex_hmac_sha1(k, d) {\n  return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction b64_hmac_sha1(k, d) {\n  return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction any_hmac_sha1(k, d, e) {\n  return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e);\n}\n\n/*\n * Perform a simple self-test to see if the VM is working\n */\nfunction sha1_vm_test() {\n  return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n\n/*\n * Calculate the SHA1 of a raw string\n */\nfunction rstr_sha1(s) {\n  return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */\nfunction rstr_hmac_sha1(key, data) {\n  var bkey = rstr2binb(key);\n  if (bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n  var ipad = Array(16),\n    opad = Array(16);\n  for (var i = 0; i < 16; i++) {\n    ipad[i] = bkey[i] ^ 0x36363636;\n    opad[i] = bkey[i] ^ 0x5C5C5C5C;\n  }\n  var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n  return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n\n/*\n * Convert a raw string to a hex string\n */\nfunction rstr2hex(input) {\n  try {\n    hexcase;\n  } catch (e) {\n    hexcase = 0;\n  }\n  var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n  var output = \"\";\n  var x;\n  for (var i = 0; i < input.length; i++) {\n    x = input.charCodeAt(i);\n    output += hex_tab.charAt(x >>> 4 & 0x0F) + hex_tab.charAt(x & 0x0F);\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to a base-64 string\n */\nfunction rstr2b64(input) {\n  try {\n    b64pad;\n  } catch (e) {\n    b64pad = '';\n  }\n  var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n  var output = \"\";\n  var len = input.length;\n  for (var i = 0; i < len; i += 3) {\n    var triplet = input.charCodeAt(i) << 16 | (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) | (i + 2 < len ? input.charCodeAt(i + 2) : 0);\n    for (var j = 0; j < 4; j++) {\n      if (i * 8 + j * 6 > input.length * 8) output += b64pad;else output += tab.charAt(triplet >>> 6 * (3 - j) & 0x3F);\n    }\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to an arbitrary string encoding\n */\nfunction rstr2any(input, encoding) {\n  var divisor = encoding.length;\n  var remainders = Array();\n  var i, q, x, quotient;\n\n  /* Convert to an array of 16-bit big-endian values, forming the dividend */\n  var dividend = Array(Math.ceil(input.length / 2));\n  for (i = 0; i < dividend.length; i++) {\n    dividend[i] = input.charCodeAt(i * 2) << 8 | input.charCodeAt(i * 2 + 1);\n  }\n\n  /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */\n  while (dividend.length > 0) {\n    quotient = Array();\n    x = 0;\n    for (i = 0; i < dividend.length; i++) {\n      x = (x << 16) + dividend[i];\n      q = Math.floor(x / divisor);\n      x -= q * divisor;\n      if (quotient.length > 0 || q > 0) quotient[quotient.length] = q;\n    }\n    remainders[remainders.length] = x;\n    dividend = quotient;\n  }\n\n  /* Convert the remainders to the output string */\n  var output = \"\";\n  for (i = remainders.length - 1; i >= 0; i--) output += encoding.charAt(remainders[i]);\n\n  /* Append leading zero equivalents */\n  var full_length = Math.ceil(input.length * 8 / (Math.log(encoding.length) / Math.log(2)));\n  for (i = output.length; i < full_length; i++) output = encoding[0] + output;\n  return output;\n}\n\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */\nfunction str2rstr_utf8(input) {\n  var output = \"\";\n  var i = -1;\n  var x, y;\n  while (++i < input.length) {\n    /* Decode utf-16 surrogate pairs */\n    x = input.charCodeAt(i);\n    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n    if (0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF) {\n      x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n      i++;\n    }\n\n    /* Encode output as utf-8 */\n    if (x <= 0x7F) output += String.fromCharCode(x);else if (x <= 0x7FF) output += String.fromCharCode(0xC0 | x >>> 6 & 0x1F, 0x80 | x & 0x3F);else if (x <= 0xFFFF) output += String.fromCharCode(0xE0 | x >>> 12 & 0x0F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);else if (x <= 0x1FFFFF) output += String.fromCharCode(0xF0 | x >>> 18 & 0x07, 0x80 | x >>> 12 & 0x3F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n  }\n  return output;\n}\n\n/*\n * Encode a string as utf-16\n */\nfunction str2rstr_utf16le(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length; i++) output += String.fromCharCode(input.charCodeAt(i) & 0xFF, input.charCodeAt(i) >>> 8 & 0xFF);\n  return output;\n}\nfunction str2rstr_utf16be(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length; i++) output += String.fromCharCode(input.charCodeAt(i) >>> 8 & 0xFF, input.charCodeAt(i) & 0xFF);\n  return output;\n}\n\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input) {\n  var output = Array(input.length >> 2);\n  for (var i = 0; i < output.length; i++) output[i] = 0;\n  for (var i = 0; i < input.length * 8; i += 8) output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << 24 - i % 32;\n  return output;\n}\n\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length * 32; i += 8) output += String.fromCharCode(input[i >> 5] >>> 24 - i % 32 & 0xFF);\n  return output;\n}\n\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */\nfunction binb_sha1(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << 24 - len % 32;\n  x[(len + 64 >> 9 << 4) + 15] = len;\n  var w = Array(80);\n  var a = 1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d = 271733878;\n  var e = -1009589776;\n  for (var i = 0; i < x.length; i += 16) {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    var olde = e;\n    for (var j = 0; j < 80; j++) {\n      if (j < 16) w[j] = x[i + j];else w[j] = bit_rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)), safe_add(safe_add(e, w[j]), sha1_kt(j)));\n      e = d;\n      d = c;\n      c = bit_rol(b, 30);\n      b = a;\n      a = t;\n    }\n    a = safe_add(a, olda);\n    b = safe_add(b, oldb);\n    c = safe_add(c, oldc);\n    d = safe_add(d, oldd);\n    e = safe_add(e, olde);\n  }\n  return Array(a, b, c, d, e);\n}\n\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */\nfunction sha1_ft(t, b, c, d) {\n  if (t < 20) return b & c | ~b & d;\n  if (t < 40) return b ^ c ^ d;\n  if (t < 60) return b & c | b & d | c & d;\n  return b ^ c ^ d;\n}\n\n/*\n * Determine the appropriate additive constant for the current iteration\n */\nfunction sha1_kt(t) {\n  return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;\n}\n\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\nfunction safe_add(x, y) {\n  var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xFFFF;\n}\n\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\nfunction bit_rol(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\nexports.HMACSHA1 = function (key, data) {\n  return b64_hmac_sha1(key, data);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;