"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oidc-token-hash";
exports.ids = ["vendor-chunks/oidc-token-hash"];
exports.modules = {

/***/ "(rsc)/./node_modules/oidc-token-hash/lib/index.js":
/*!***************************************************!*\
  !*** ./node_modules/oidc-token-hash/lib/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  strict: assert\n} = __webpack_require__(/*! assert */ \"assert\");\nconst {\n  createHash\n} = __webpack_require__(/*! crypto */ \"crypto\");\nconst {\n  format\n} = __webpack_require__(/*! util */ \"util\");\nconst shake256 = __webpack_require__(/*! ./shake256 */ \"(rsc)/./node_modules/oidc-token-hash/lib/shake256.js\");\nlet encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = input => input.toString('base64url');\n} else {\n  const fromBase64 = base64 => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = input => fromBase64(input.toString('base64'));\n}\n\n/** SPECIFICATION\n * Its (_hash) value is the base64url encoding of the left-most half of the hash of the octets of\n * the ASCII representation of the token value, where the hash algorithm used is the hash algorithm\n * used in the alg Header Parameter of the ID Token's JOSE Header. For instance, if the alg is\n * RS256, hash the token value with SHA-256, then take the left-most 128 bits and base64url encode\n * them. The _hash value is a case sensitive string.\n */\n\n/**\n * @name getHash\n * @api private\n *\n * returns the sha length based off the JOSE alg heade value, defaults to sha256\n *\n * @param token {String} token value to generate the hash from\n * @param alg {String} ID Token JOSE header alg value (i.e. RS256, HS384, ES512, PS256)\n * @param [crv] {String} For EdDSA the curve decides what hash algorithm is used. Required for EdDSA\n */\nfunction getHash(alg, crv) {\n  switch (alg) {\n    case 'HS256':\n    case 'RS256':\n    case 'PS256':\n    case 'ES256':\n    case 'ES256K':\n      return createHash('sha256');\n    case 'HS384':\n    case 'RS384':\n    case 'PS384':\n    case 'ES384':\n      return createHash('sha384');\n    case 'HS512':\n    case 'RS512':\n    case 'PS512':\n    case 'ES512':\n    case 'Ed25519':\n      return createHash('sha512');\n    case 'Ed448':\n      if (!shake256) {\n        throw new TypeError('Ed448 *_hash calculation is not supported in your Node.js runtime version');\n      }\n      return createHash('shake256', {\n        outputLength: 114\n      });\n    case 'EdDSA':\n      switch (crv) {\n        case 'Ed25519':\n          return createHash('sha512');\n        case 'Ed448':\n          if (!shake256) {\n            throw new TypeError('Ed448 *_hash calculation is not supported in your Node.js runtime version');\n          }\n          return createHash('shake256', {\n            outputLength: 114\n          });\n        default:\n          throw new TypeError('unrecognized or invalid EdDSA curve provided');\n      }\n    default:\n      throw new TypeError('unrecognized or invalid JWS algorithm provided');\n  }\n}\nfunction generate(token, alg, crv) {\n  const digest = getHash(alg, crv).update(token).digest();\n  return encode(digest.slice(0, digest.length / 2));\n}\nfunction validate(names, actual, source, alg, crv) {\n  if (typeof names.claim !== 'string' || !names.claim) {\n    throw new TypeError('names.claim must be a non-empty string');\n  }\n  if (typeof names.source !== 'string' || !names.source) {\n    throw new TypeError('names.source must be a non-empty string');\n  }\n  assert(typeof actual === 'string' && actual, `${names.claim} must be a non-empty string`);\n  assert(typeof source === 'string' && source, `${names.source} must be a non-empty string`);\n  let expected;\n  let msg;\n  try {\n    expected = generate(source, alg, crv);\n  } catch (err) {\n    msg = format('%s could not be validated (%s)', names.claim, err.message);\n  }\n  msg = msg || format('%s mismatch, expected %s, got: %s', names.claim, expected, actual);\n  assert.equal(expected, actual, msg);\n}\nmodule.exports = {\n  validate,\n  generate\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oidc-token-hash/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oidc-token-hash/lib/shake256.js":
/*!******************************************************!*\
  !*** ./node_modules/oidc-token-hash/lib/shake256.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst [major, minor] = process.version.substring(1).split('.').map(x => parseInt(x, 10));\nconst xofOutputLength = major > 12 || major === 12 && minor >= 8;\nconst shake256 = xofOutputLength && crypto.getHashes().includes('shake256');\nmodule.exports = shake256;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2lkYy10b2tlbi1oYXNoL2xpYi9zaGFrZTI1Ni5qcyIsIm1hcHBpbmdzIjoiOztBQUFBLE1BQU1BLE1BQU0sR0FBR0MsbUJBQU8sQ0FBQyxzQkFBUSxDQUFDO0FBRWhDLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFQyxLQUFLLENBQUMsR0FBR0MsT0FBTyxDQUFDQyxPQUFPLENBQUNDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHLENBQUVDLENBQUMsSUFBS0MsUUFBUSxDQUFDRCxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7QUFDMUYsTUFBTUUsZUFBZSxHQUFHVCxLQUFLLEdBQUcsRUFBRSxJQUFLQSxLQUFLLEtBQUssRUFBRSxJQUFJQyxLQUFLLElBQUksQ0FBRTtBQUNsRSxNQUFNUyxRQUFRLEdBQUdELGVBQWUsSUFBSVgsTUFBTSxDQUFDYSxTQUFTLENBQUMsQ0FBQyxDQUFDQyxRQUFRLENBQUMsVUFBVSxDQUFDO0FBRTNFQyxNQUFNLENBQUNDLE9BQU8sR0FBR0osUUFBUSIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9pZGMtdG9rZW4taGFzaFxcbGliXFxzaGFrZTI1Ni5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxuY29uc3QgW21ham9yLCBtaW5vcl0gPSBwcm9jZXNzLnZlcnNpb24uc3Vic3RyaW5nKDEpLnNwbGl0KCcuJykubWFwKCh4KSA9PiBwYXJzZUludCh4LCAxMCkpO1xuY29uc3QgeG9mT3V0cHV0TGVuZ3RoID0gbWFqb3IgPiAxMiB8fCAobWFqb3IgPT09IDEyICYmIG1pbm9yID49IDgpO1xuY29uc3Qgc2hha2UyNTYgPSB4b2ZPdXRwdXRMZW5ndGggJiYgY3J5cHRvLmdldEhhc2hlcygpLmluY2x1ZGVzKCdzaGFrZTI1NicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNoYWtlMjU2O1xuIl0sIm5hbWVzIjpbImNyeXB0byIsInJlcXVpcmUiLCJtYWpvciIsIm1pbm9yIiwicHJvY2VzcyIsInZlcnNpb24iLCJzdWJzdHJpbmciLCJzcGxpdCIsIm1hcCIsIngiLCJwYXJzZUludCIsInhvZk91dHB1dExlbmd0aCIsInNoYWtlMjU2IiwiZ2V0SGFzaGVzIiwiaW5jbHVkZXMiLCJtb2R1bGUiLCJleHBvcnRzIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oidc-token-hash/lib/shake256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oidc-token-hash/lib/index.js":
/*!***************************************************!*\
  !*** ./node_modules/oidc-token-hash/lib/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  strict: assert\n} = __webpack_require__(/*! assert */ \"assert\");\nconst {\n  createHash\n} = __webpack_require__(/*! crypto */ \"crypto\");\nconst {\n  format\n} = __webpack_require__(/*! util */ \"util\");\nconst shake256 = __webpack_require__(/*! ./shake256 */ \"(ssr)/./node_modules/oidc-token-hash/lib/shake256.js\");\nlet encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = input => input.toString('base64url');\n} else {\n  const fromBase64 = base64 => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = input => fromBase64(input.toString('base64'));\n}\n\n/** SPECIFICATION\n * Its (_hash) value is the base64url encoding of the left-most half of the hash of the octets of\n * the ASCII representation of the token value, where the hash algorithm used is the hash algorithm\n * used in the alg Header Parameter of the ID Token's JOSE Header. For instance, if the alg is\n * RS256, hash the token value with SHA-256, then take the left-most 128 bits and base64url encode\n * them. The _hash value is a case sensitive string.\n */\n\n/**\n * @name getHash\n * @api private\n *\n * returns the sha length based off the JOSE alg heade value, defaults to sha256\n *\n * @param token {String} token value to generate the hash from\n * @param alg {String} ID Token JOSE header alg value (i.e. RS256, HS384, ES512, PS256)\n * @param [crv] {String} For EdDSA the curve decides what hash algorithm is used. Required for EdDSA\n */\nfunction getHash(alg, crv) {\n  switch (alg) {\n    case 'HS256':\n    case 'RS256':\n    case 'PS256':\n    case 'ES256':\n    case 'ES256K':\n      return createHash('sha256');\n    case 'HS384':\n    case 'RS384':\n    case 'PS384':\n    case 'ES384':\n      return createHash('sha384');\n    case 'HS512':\n    case 'RS512':\n    case 'PS512':\n    case 'ES512':\n    case 'Ed25519':\n      return createHash('sha512');\n    case 'Ed448':\n      if (!shake256) {\n        throw new TypeError('Ed448 *_hash calculation is not supported in your Node.js runtime version');\n      }\n      return createHash('shake256', {\n        outputLength: 114\n      });\n    case 'EdDSA':\n      switch (crv) {\n        case 'Ed25519':\n          return createHash('sha512');\n        case 'Ed448':\n          if (!shake256) {\n            throw new TypeError('Ed448 *_hash calculation is not supported in your Node.js runtime version');\n          }\n          return createHash('shake256', {\n            outputLength: 114\n          });\n        default:\n          throw new TypeError('unrecognized or invalid EdDSA curve provided');\n      }\n    default:\n      throw new TypeError('unrecognized or invalid JWS algorithm provided');\n  }\n}\nfunction generate(token, alg, crv) {\n  const digest = getHash(alg, crv).update(token).digest();\n  return encode(digest.slice(0, digest.length / 2));\n}\nfunction validate(names, actual, source, alg, crv) {\n  if (typeof names.claim !== 'string' || !names.claim) {\n    throw new TypeError('names.claim must be a non-empty string');\n  }\n  if (typeof names.source !== 'string' || !names.source) {\n    throw new TypeError('names.source must be a non-empty string');\n  }\n  assert(typeof actual === 'string' && actual, `${names.claim} must be a non-empty string`);\n  assert(typeof source === 'string' && source, `${names.source} must be a non-empty string`);\n  let expected;\n  let msg;\n  try {\n    expected = generate(source, alg, crv);\n  } catch (err) {\n    msg = format('%s could not be validated (%s)', names.claim, err.message);\n  }\n  msg = msg || format('%s mismatch, expected %s, got: %s', names.claim, expected, actual);\n  assert.equal(expected, actual, msg);\n}\nmodule.exports = {\n  validate,\n  generate\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oidc-token-hash/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/oidc-token-hash/lib/shake256.js":
/*!******************************************************!*\
  !*** ./node_modules/oidc-token-hash/lib/shake256.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst [major, minor] = process.version.substring(1).split('.').map(x => parseInt(x, 10));\nconst xofOutputLength = major > 12 || major === 12 && minor >= 8;\nconst shake256 = xofOutputLength && crypto.getHashes().includes('shake256');\nmodule.exports = shake256;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb2lkYy10b2tlbi1oYXNoL2xpYi9zaGFrZTI1Ni5qcyIsIm1hcHBpbmdzIjoiOztBQUFBLE1BQU1BLE1BQU0sR0FBR0MsbUJBQU8sQ0FBQyxzQkFBUSxDQUFDO0FBRWhDLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFQyxLQUFLLENBQUMsR0FBR0MsT0FBTyxDQUFDQyxPQUFPLENBQUNDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHLENBQUVDLENBQUMsSUFBS0MsUUFBUSxDQUFDRCxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7QUFDMUYsTUFBTUUsZUFBZSxHQUFHVCxLQUFLLEdBQUcsRUFBRSxJQUFLQSxLQUFLLEtBQUssRUFBRSxJQUFJQyxLQUFLLElBQUksQ0FBRTtBQUNsRSxNQUFNUyxRQUFRLEdBQUdELGVBQWUsSUFBSVgsTUFBTSxDQUFDYSxTQUFTLENBQUMsQ0FBQyxDQUFDQyxRQUFRLENBQUMsVUFBVSxDQUFDO0FBRTNFQyxNQUFNLENBQUNDLE9BQU8sR0FBR0osUUFBUSIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9pZGMtdG9rZW4taGFzaFxcbGliXFxzaGFrZTI1Ni5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxuY29uc3QgW21ham9yLCBtaW5vcl0gPSBwcm9jZXNzLnZlcnNpb24uc3Vic3RyaW5nKDEpLnNwbGl0KCcuJykubWFwKCh4KSA9PiBwYXJzZUludCh4LCAxMCkpO1xuY29uc3QgeG9mT3V0cHV0TGVuZ3RoID0gbWFqb3IgPiAxMiB8fCAobWFqb3IgPT09IDEyICYmIG1pbm9yID49IDgpO1xuY29uc3Qgc2hha2UyNTYgPSB4b2ZPdXRwdXRMZW5ndGggJiYgY3J5cHRvLmdldEhhc2hlcygpLmluY2x1ZGVzKCdzaGFrZTI1NicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNoYWtlMjU2O1xuIl0sIm5hbWVzIjpbImNyeXB0byIsInJlcXVpcmUiLCJtYWpvciIsIm1pbm9yIiwicHJvY2VzcyIsInZlcnNpb24iLCJzdWJzdHJpbmciLCJzcGxpdCIsIm1hcCIsIngiLCJwYXJzZUludCIsInhvZk91dHB1dExlbmd0aCIsInNoYWtlMjU2IiwiZ2V0SGFzaGVzIiwiaW5jbHVkZXMiLCJtb2R1bGUiLCJleHBvcnRzIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oidc-token-hash/lib/shake256.js\n");

/***/ })

};
;