'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2, History, Mail, Search, Filter, RefreshCw } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { format } from 'date-fns';

interface NotificationQueueItem {
  id: string;
  recipientEmail: string;
  templateName: string;
  priority: string;
  status: string;
  attempts: number;
  sentAt: string | null;
  failureReason: string | null;
  createdAt: string;
  event: {
    eventType: string;
    entityType: string;
    customer?: {
      name: string;
    };
    executive?: {
      name: string;
    };
  };
}

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  PROCESSING: 'bg-blue-100 text-blue-800',
  SENT: 'bg-green-100 text-green-800',
  FAILED: 'bg-red-100 text-red-800',
  CANCELLED: 'bg-gray-100 text-gray-800',
};

const priorityColors = {
  HIGH: 'bg-red-100 text-red-800',
  NORMAL: 'bg-blue-100 text-blue-800',
  LOW: 'bg-gray-100 text-gray-800',
};

export default function NotificationHistoryPage() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<NotificationQueueItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    eventType: '',
    search: '',
  });
  const [pagination, setPagination] = useState({
    skip: 0,
    take: 20,
    total: 0,
    hasMore: false,
  });

  useEffect(() => {
    fetchNotifications();
  }, [filters, pagination.skip]);

  const fetchNotifications = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const params = new URLSearchParams({
        skip: pagination.skip.toString(),
        take: pagination.take.toString(),
      });

      if (filters.status) params.append('status', filters.status);
      if (filters.priority) params.append('priority', filters.priority);
      if (filters.eventType) params.append('eventType', filters.eventType);

      const response = await fetch(`/api/notifications/queue?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in.');
        } else if (response.status === 403) {
          throw new Error('Access denied. Insufficient permissions.');
        } else {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to fetch notification history (${response.status})`);
        }
      }

      const data = await response.json();
      
      if (refresh || pagination.skip === 0) {
        setNotifications(data.data);
      } else {
        setNotifications(prev => [...prev, ...data.data]);
      }
      
      setPagination(prev => ({
        ...prev,
        total: data.pagination.total,
        hasMore: data.pagination.hasMore,
      }));
    } catch (error) {
      console.error('Error fetching notification history:', error);
      toast.error('Failed to load notification history');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, skip: 0 }));
  };

  const loadMore = () => {
    setPagination(prev => ({
      ...prev,
      skip: prev.skip + prev.take,
    }));
  };

  const refresh = () => {
    setPagination(prev => ({ ...prev, skip: 0 }));
    fetchNotifications(true);
  };

  const getEventTypeLabel = (eventType: string) => {
    const labels: Record<string, string> = {
      'LEAD_CREATED': 'Lead Created',
      'LEAD_STATUS_CHANGED': 'Lead Status Changed',
      'OPPORTUNITY_CREATED': 'Opportunity Created',
      'OPPORTUNITY_STATUS_CHANGED': 'Opportunity Status Changed',
      'PROSPECT_CREATED': 'Prospect Created',
      'PROSPECT_STATUS_CHANGED': 'Prospect Status Changed',
      'ORDER_CREATED': 'Order Created',
      'ORDER_STATUS_CHANGED': 'Order Status Changed',
      'CONVERSION_EVENT': 'Conversion Event',
    };
    return labels[eventType] || eventType;
  };

  return (
    <DashboardLayout
      title="Notification History"
      breadcrumbs={[
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Notifications', href: '/notifications' },
        { label: 'History' },
      ]}
      requireAuth
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
    >
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-primary text-primary-foreground">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <div>
                  <CardTitle>Email Notification History</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    View your email notification delivery history and status
                  </CardDescription>
                </div>
              </div>
              <Button
                variant="secondary"
                size="sm"
                onClick={refresh}
                disabled={refreshing}
              >
                {refreshing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="PROCESSING">Processing</SelectItem>
                    <SelectItem value="SENT">Sent</SelectItem>
                    <SelectItem value="FAILED">Failed</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Priority</label>
                <Select value={filters.priority} onValueChange={(value) => handleFilterChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All priorities</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="NORMAL">Normal</SelectItem>
                    <SelectItem value="LOW">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Event Type</label>
                <Select value={filters.eventType} onValueChange={(value) => handleFilterChange('eventType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All events" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All events</SelectItem>
                    <SelectItem value="LEAD_CREATED">Lead Created</SelectItem>
                    <SelectItem value="LEAD_STATUS_CHANGED">Lead Status Changed</SelectItem>
                    <SelectItem value="OPPORTUNITY_CREATED">Opportunity Created</SelectItem>
                    <SelectItem value="OPPORTUNITY_STATUS_CHANGED">Opportunity Status Changed</SelectItem>
                    <SelectItem value="ORDER_CREATED">Order Created</SelectItem>
                    <SelectItem value="ORDER_STATUS_CHANGED">Order Status Changed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search notifications..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Notifications List */}
            {loading && notifications.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : notifications.length === 0 ? (
              <div className="text-center py-12">
                <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-muted-foreground mb-2">No notifications found</h3>
                <p className="text-sm text-muted-foreground">
                  No email notifications match your current filters.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <div key={notification.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={statusColors[notification.status as keyof typeof statusColors]}>
                            {notification.status}
                          </Badge>
                          <Badge className={priorityColors[notification.priority as keyof typeof priorityColors]}>
                            {notification.priority}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {getEventTypeLabel(notification.event.eventType)}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <p className="font-medium">{notification.templateName}</p>
                          <p className="text-sm text-muted-foreground">
                            To: {notification.recipientEmail}
                          </p>
                          {notification.event.customer && (
                            <p className="text-sm text-muted-foreground">
                              Customer: {notification.event.customer.name}
                            </p>
                          )}
                          {notification.event.executive && (
                            <p className="text-sm text-muted-foreground">
                              Executive: {notification.event.executive.name}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-right text-sm text-muted-foreground">
                        <p>Created: {format(new Date(notification.createdAt), 'MMM dd, yyyy HH:mm')}</p>
                        {notification.sentAt && (
                          <p>Sent: {format(new Date(notification.sentAt), 'MMM dd, yyyy HH:mm')}</p>
                        )}
                        {notification.attempts > 0 && (
                          <p>Attempts: {notification.attempts}</p>
                        )}
                        {notification.failureReason && (
                          <p className="text-red-600 text-xs mt-1">
                            Error: {notification.failureReason}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {pagination.hasMore && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={loadMore}
                      disabled={loading}
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : null}
                      Load More
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
