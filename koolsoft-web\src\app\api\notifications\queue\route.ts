import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesNotificationQueueRepository } from '@/lib/repositories';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { notificationQueueFilterSchema, processNotificationsSchema } from '@/lib/validations/notification.schema';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * GET /api/notifications/queue
 * Get notification queue with filtering and pagination
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const session = await getServerSession(authOptions);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = notificationQueueFilterSchema.parse(queryParams);

      const salesNotificationQueueRepository = getSalesNotificationQueueRepository();

      // For non-admin users, filter to their own notifications
      if (session?.user?.role && !['ADMIN', 'MANAGER'].includes(session.user.role.toUpperCase())) {
        validatedParams.recipientUserId = session.user.id;
      }

      // Build filter object
      const filter: any = {};
      if (validatedParams.status) filter.status = validatedParams.status;
      if (validatedParams.priority) filter.priority = validatedParams.priority;
      if (validatedParams.recipientUserId) filter.recipientUserId = validatedParams.recipientUserId;
      if (validatedParams.eventType) {
        filter.event = { eventType: validatedParams.eventType };
      }
      if (validatedParams.entityType) {
        filter.event = { ...filter.event, entityType: validatedParams.entityType };
      }
      if (validatedParams.startDate || validatedParams.endDate) {
        filter.createdAt = {};
        if (validatedParams.startDate) filter.createdAt.gte = validatedParams.startDate;
        if (validatedParams.endDate) filter.createdAt.lte = validatedParams.endDate;
      }

      // Get notifications with pagination using repository methods
      const notifications = await salesNotificationQueueRepository.findBy(
        filter,
        validatedParams.skip,
        validatedParams.take
      );

      // Get total count for pagination
      const totalCount = await salesNotificationQueueRepository.count(filter);

      return NextResponse.json({
        success: true,
        data: notifications,
        pagination: {
          skip: validatedParams.skip,
          take: validatedParams.take,
          total: totalCount,
          hasMore: validatedParams.skip + validatedParams.take < totalCount,
        },
      });
    } catch (error) {
      console.error('Error fetching notification queue:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch notification queue',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/notifications/queue/process
 * Process pending notifications in the queue (Admin/Manager only)
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const validatedData = processNotificationsSchema.parse(body);

      const salesNotificationService = getSalesNotificationService();
      const processedCount = await salesNotificationService.processPendingNotifications(validatedData.limit);

      return NextResponse.json({
        success: true,
        message: `Processed ${processedCount} notifications`,
        processedCount,
      });
    } catch (error) {
      console.error('Error processing notification queue:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to process notification queue',
        },
        { status: 500 }
      );
    }
  }
);
