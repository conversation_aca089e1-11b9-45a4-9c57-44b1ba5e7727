import { PrismaClient, Prisma } from '@prisma/client';
import { PrismaRepository } from './prisma.repository';

/**
 * Sales Notification Queue Repository
 *
 * This repository handles database operations for the Sales Notification Queue entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class SalesNotificationQueueRepository extends PrismaRepository<
  Prisma.SalesNotificationQueueGetPayload<{}>,
  string,
  Prisma.SalesNotificationQueueCreateInput,
  Prisma.SalesNotificationQueueUpdateInput
> {
  createTransactionRepository(tx: any): SalesNotificationQueueRepository {
    return new SalesNotificationQueueRepository(tx);
  }
  constructor(prismaClient?: PrismaClient) {
    super('SalesNotificationQueue');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Add notification to queue
   * @param queueData Queue data
   * @returns Promise resolving to the created queue item
   */
  async addToQueue(queueData: {
    eventId: string;
    recipientUserId: string;
    recipientEmail: string;
    templateName: string;
    templateData: any;
    priority?: string;
    scheduledFor?: Date;
  }): Promise<Prisma.SalesNotificationQueueGetPayload<{}>> {
    return this.model.create({
      data: {
        eventId: queueData.eventId,
        recipientUserId: queueData.recipientUserId,
        recipientEmail: queueData.recipientEmail,
        templateName: queueData.templateName,
        templateData: queueData.templateData,
        priority: queueData.priority || 'NORMAL',
        scheduledFor: queueData.scheduledFor,
      },
      include: {
        event: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          },
        },
        recipientUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Get pending notifications ready for processing
   * @param limit Maximum number of notifications to return
   * @returns Promise resolving to an array of pending notifications
   */
  async getPendingNotifications(limit: number = 50): Promise<Prisma.SalesNotificationQueueGetPayload<{
    include: {
      event: {
        include: {
          user: { select: { id: true; name: true; email: true; role: true } };
          customer: { select: { id: true; name: true; email: true; phone: true } };
          executive: { select: { id: true; name: true; email: true; designation: true } };
        };
      };
      recipientUser: { select: { id: true; name: true; email: true; role: true } };
    };
  }>[]> {
    const now = new Date();

    return this.model.findMany({
      where: {
        status: 'PENDING',
        attempts: {
          lt: 3, // maxAttempts default value
        },
        OR: [
          { scheduledFor: null },
          { scheduledFor: { lte: now } },
        ],
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' },
      ],
      take: limit,
      include: {
        event: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          },
        },
        recipientUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Update notification status
   * @param id Notification ID
   * @param status New status
   * @param additionalData Additional data to update
   * @returns Promise resolving to the updated notification
   */
  async updateStatus(
    id: string,
    status: string,
    additionalData?: {
      attempts?: number;
      lastAttemptAt?: Date;
      sentAt?: Date;
      failureReason?: string;
      emailLogId?: string;
    }
  ): Promise<Prisma.SalesNotificationQueueGetPayload<{}>> {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (additionalData) {
      Object.assign(updateData, additionalData);
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        event: true,
        recipientUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Increment attempt count
   * @param id Notification ID
   * @param failureReason Reason for failure (optional)
   * @returns Promise resolving to the updated notification
   */
  async incrementAttempt(id: string, failureReason?: string): Promise<Prisma.SalesNotificationQueueGetPayload<{}>> {
    const notification = await this.model.findUnique({
      where: { id },
      select: { attempts: true, maxAttempts: true },
    });

    if (!notification) {
      throw new Error(`Notification with ID ${id} not found`);
    }

    const newAttempts = notification.attempts + 1;
    const newStatus = newAttempts >= notification.maxAttempts ? 'FAILED' : 'PENDING';

    return this.model.update({
      where: { id },
      data: {
        attempts: newAttempts,
        status: newStatus,
        lastAttemptAt: new Date(),
        failureReason,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Find notifications by event
   * @param eventId Event ID
   * @returns Promise resolving to an array of notifications
   */
  async findByEvent(eventId: string): Promise<Prisma.SalesNotificationQueueGetPayload<{
    include: {
      recipientUser: { select: { id: true; name: true; email: true; role: true } };
    };
  }>[]> {
    return this.model.findMany({
      where: { eventId },
      orderBy: { createdAt: 'desc' },
      include: {
        recipientUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Find notifications by recipient
   * @param recipientUserId Recipient user ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of notifications
   */
  async findByRecipient(
    recipientUserId: string,
    skip?: number,
    take?: number
  ): Promise<Prisma.SalesNotificationQueueGetPayload<{
    include: {
      event: {
        include: {
          customer: { select: { id: true; name: true; email: true; phone: true } };
          executive: { select: { id: true; name: true; email: true; designation: true } };
        };
      };
    };
  }>[]> {
    return this.model.findMany({
      where: { recipientUserId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        event: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Get queue statistics
   * @param startDate Start date for statistics
   * @param endDate End date for statistics
   * @returns Promise resolving to queue statistics
   */
  async getStatistics(startDate?: Date, endDate?: Date): Promise<{
    totalNotifications: number;
    pendingNotifications: number;
    sentNotifications: number;
    failedNotifications: number;
    notificationsByStatus: Record<string, number>;
    notificationsByPriority: Record<string, number>;
    averageAttempts: number;
  }> {
    const where: any = {};
    
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [totalNotifications, allNotifications] = await Promise.all([
      this.model.count({ where }),
      this.model.findMany({
        where,
        select: {
          status: true,
          priority: true,
          attempts: true,
        },
      }),
    ]);

    const notificationsByStatus: Record<string, number> = {};
    const notificationsByPriority: Record<string, number> = {};
    let totalAttempts = 0;

    allNotifications.forEach(notification => {
      notificationsByStatus[notification.status] = (notificationsByStatus[notification.status] || 0) + 1;
      notificationsByPriority[notification.priority] = (notificationsByPriority[notification.priority] || 0) + 1;
      totalAttempts += notification.attempts;
    });

    const averageAttempts = totalNotifications > 0 ? totalAttempts / totalNotifications : 0;

    return {
      totalNotifications,
      pendingNotifications: notificationsByStatus['PENDING'] || 0,
      sentNotifications: notificationsByStatus['SENT'] || 0,
      failedNotifications: notificationsByStatus['FAILED'] || 0,
      notificationsByStatus,
      notificationsByPriority,
      averageAttempts,
    };
  }

  /**
   * Clean up old notifications
   * @param olderThanDays Delete notifications older than this many days
   * @param statusesToClean Statuses to clean up (default: ['SENT', 'FAILED'])
   * @returns Promise resolving to the number of deleted notifications
   */
  async cleanupOldNotifications(
    olderThanDays: number = 30,
    statusesToClean: string[] = ['SENT', 'FAILED']
  ): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.model.deleteMany({
      where: {
        status: {
          in: statusesToClean,
        },
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }
}

/**
 * Get the sales notification queue repository instance
 * @returns Sales notification queue repository instance
 */
export function getSalesNotificationQueueRepository(): SalesNotificationQueueRepository {
  return new SalesNotificationQueueRepository();
}
